# EXIM Clearance System - Quick Start Guide

## 🚀 Get Started in 5 Minutes

### Step 1: Install Dependencies
```bash
npm install
```

### Step 2: Start Development Server
```bash
npm run dev
```

### Step 3: Open in Browser
Navigate to `http://localhost:3000`

## 🔑 Default Login Credentials

Since this is a frontend-only implementation with mock data, you can use any credentials to log in. The authentication system is fully implemented but uses mock responses.

**Example credentials:**
- Email: `<EMAIL>`
- Password: `password123`

## 🎯 Key Features to Explore

### 1. Dashboard
- View comprehensive statistics and charts
- Monitor system performance metrics
- Check recent applications and activities

### 2. Applications
- Create new applications with the multi-step form
- View and manage existing applications
- Use advanced search and filtering

### 3. Navigation
- Explore role-based navigation menu
- Test responsive design on different screen sizes
- Try the collapsible sidebar

### 4. Components
- Interactive data tables with sorting/filtering
- File upload with drag-and-drop
- Status chips and progress indicators
- Real-time notifications

## 🛠️ Development Commands

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run linting
npm run lint

# Run tests
npm test

# Run tests with coverage
npm run test:coverage
```

## 📱 Testing Responsive Design

1. **Desktop**: Full sidebar navigation with all features
2. **Tablet**: Collapsible sidebar with touch-friendly interface
3. **Mobile**: Drawer navigation with optimized layouts

Use browser dev tools to test different screen sizes.

## 🎨 Customizing the Theme

Edit `src/theme/index.ts` to customize:
- Colors and palette
- Typography and fonts
- Component styles
- Spacing and borders

## 📊 Mock Data

The application uses mock data for demonstration:
- Applications with different statuses
- Dashboard statistics and charts
- User profiles and notifications

## 🔧 Environment Configuration

Copy `.env.example` to `.env` and configure:
```bash
VITE_API_BASE_URL=http://localhost:4000/api
VITE_MOCK_API=true
VITE_DEBUG_MODE=false
```

## 🚢 Docker Deployment

```bash
# Build Docker image
docker build -t exim-clearance-system .

# Run container
docker run -p 3000:3000 exim-clearance-system
```

## 📝 Next Steps

1. **Backend Integration**: Connect to real API endpoints
2. **Authentication**: Implement real authentication service
3. **Database**: Set up database for persistent storage
4. **Payment**: Integrate payment gateways
5. **Notifications**: Set up real-time notification service

## 🆘 Troubleshooting

### Common Issues

**Port already in use:**
```bash
# Kill process on port 3000
npx kill-port 3000
```

**Dependencies issues:**
```bash
# Clear npm cache and reinstall
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

**Build errors:**
```bash
# Check TypeScript errors
npx tsc --noEmit
```

## 📚 Documentation

- [Implementation Summary](./IMPLEMENTATION_SUMMARY.md)
- [README](./README.md)
- [Component Documentation](./src/components/README.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

---

**Happy coding! 🎉**
