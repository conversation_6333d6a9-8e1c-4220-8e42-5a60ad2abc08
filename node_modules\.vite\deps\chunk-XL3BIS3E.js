import {
  DefaultPropsProvider_default,
  _extends,
  init_extends,
  require_prop_types,
  useDefaultProps
} from "./chunk-R4KUSUFF.js";
import {
  require_jsx_runtime
} from "./chunk-FVZZTKNL.js";
import {
  __toESM,
  require_react
} from "./chunk-N6MYFXC3.js";

// node_modules/@mui/material/DefaultPropsProvider/DefaultPropsProvider.js
init_extends();
var React = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var import_jsx_runtime = __toESM(require_jsx_runtime());
function DefaultPropsProvider(props) {
  return (0, import_jsx_runtime.jsx)(DefaultPropsProvider_default, _extends({}, props));
}
true ? DefaultPropsProvider.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * @ignore
   */
  children: import_prop_types.default.node,
  /**
   * @ignore
   */
  value: import_prop_types.default.object.isRequired
} : void 0;
function useDefaultProps2(params) {
  return useDefaultProps(params);
}

export {
  useDefaultProps2 as useDefaultProps
};
//# sourceMappingURL=chunk-XL3BIS3E.js.map
