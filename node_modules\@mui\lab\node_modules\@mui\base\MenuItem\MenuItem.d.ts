import { PolymorphicComponent } from '../utils/PolymorphicComponent';
import { MenuItemTypeMap } from './MenuItem.types';
/**
 * An unstyled menu item to be used within a Menu.
 *
 * Demos:
 *
 * - [Menu](https://mui.com/base-ui/react-menu/)
 *
 * API:
 *
 * - [MenuItem API](https://mui.com/base-ui/react-menu/components-api/#menu-item)
 */
declare const MenuItem: PolymorphicComponent<MenuItemTypeMap>;
export { MenuItem };
