{"name": "exim-clearance-system", "private": true, "version": "1.0.0", "type": "module", "description": "EXIM Clearance Certification System - Advanced UI for import/export clearance operations", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@mui/material": "^5.14.18", "@mui/icons-material": "^5.14.18", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-data-grid": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "@mui/lab": "^5.0.0-alpha.155", "axios": "^1.6.2", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.3.3", "react-dropzone": "^14.2.3", "recharts": "^2.8.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "react-qr-code": "^2.0.12", "qrcode.js": "^0.0.0", "socket.io-client": "^4.7.4", "react-toastify": "^9.1.3", "framer-motion": "^10.16.5", "react-beautiful-dnd": "^13.1.1", "react-pdf": "^7.5.1", "react-signature-canvas": "^1.0.6", "crypto-js": "^4.2.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/lodash": "^4.14.202", "@types/crypto-js": "^4.2.1", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^1.0.0", "@vitest/ui": "^1.0.0", "@vitest/coverage-v8": "^1.0.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}