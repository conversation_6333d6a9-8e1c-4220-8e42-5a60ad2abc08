{"name": "exim-clearance-system", "private": true, "version": "1.0.0", "type": "module", "description": "EXIM Clearance Certification System - Advanced UI for import/export clearance operations", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.3.2", "@mui/icons-material": "^5.14.18", "@mui/material": "^5.14.18", "@mui/x-data-grid": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "@tanstack/react-query": "^4.36.1", "axios": "^1.6.2", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "lodash": "^4.17.21", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-router-dom": "^6.20.1", "react-toastify": "^9.1.3", "recharts": "^2.8.0", "yup": "^1.3.3"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^7.0.4"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}