import React from 'react'
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom'
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  AppBar,
  Toolbar,
  Container,
  Paper,
  Chip,
  Tabs,
  Tab,
  Breadcrumbs,
  Link,
} from '@mui/material'
import {
  Dashboard as DashboardIcon,
  Description as ApplicationsIcon,
  Payment as PaymentIcon,
  LocalShipping as ShippingIcon,
  Home as HomeIcon,
  Add as AddIcon,
} from '@mui/icons-material'

// Home Page Component
const HomePage: React.FC = () => {
  const navigate = useNavigate()

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      {/* Welcome Section */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h3" gutterBottom>
          Welcome to EXIM Clearance System
        </Typography>
        <Typography variant="h6" color="text.secondary" paragraph>
          Advanced UI for Import/Export Clearance Operations
        </Typography>
        <Typography variant="body1" paragraph>
          This is a comprehensive platform for managing import/export clearance operations
          with multi-stakeholder support, real-time tracking, and advanced analytics.
        </Typography>
      </Paper>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ cursor: 'pointer' }} onClick={() => navigate('/dashboard')}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <DashboardIcon color="primary" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 700 }}>
                    1,247
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Applications
                  </Typography>
                </Box>
              </Box>
              <Chip label="+12%" size="small" color="success" />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ cursor: 'pointer' }} onClick={() => navigate('/applications')}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ApplicationsIcon color="warning" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 700 }}>
                    89
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Pending Review
                  </Typography>
                </Box>
              </Box>
              <Chip label="-5%" size="small" color="error" />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ cursor: 'pointer' }} onClick={() => navigate('/tracking')}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ShippingIcon color="success" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 700 }}>
                    1,158
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Completed
                  </Typography>
                </Box>
              </Box>
              <Chip label="+8%" size="small" color="success" />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PaymentIcon color="info" sx={{ mr: 2, fontSize: 40 }} />
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 700 }}>
                    ₦2.45M
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Revenue
                  </Typography>
                </Box>
              </Box>
              <Chip label="+15%" size="small" color="success" />
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Features Section */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom>
              Key Features
            </Typography>
            <Box component="ul" sx={{ pl: 2 }}>
              <li>Multi-stakeholder portal system</li>
              <li>Advanced authentication with MFA</li>
              <li>Real-time cargo tracking</li>
              <li>AI-enhanced document management</li>
              <li>Payment gateway integration</li>
              <li>Comprehensive reporting</li>
              <li>Blockchain audit trails</li>
              <li>Mobile-responsive design</li>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom>
              Technology Stack
            </Typography>
            <Box component="ul" sx={{ pl: 2 }}>
              <li>React 18 with TypeScript</li>
              <li>Material-UI (MUI) components</li>
              <li>Framer Motion animations</li>
              <li>React Query for data fetching</li>
              <li>React Hook Form validation</li>
              <li>Recharts for visualization</li>
              <li>Docker containerization</li>
              <li>CI/CD with GitHub Actions</li>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Action Buttons */}
      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Button
          variant="contained"
          size="large"
          sx={{ mr: 2, mb: 2 }}
          startIcon={<AddIcon />}
          onClick={() => navigate('/applications/new')}
        >
          New Application
        </Button>
        <Button
          variant="outlined"
          size="large"
          sx={{ mr: 2, mb: 2 }}
          startIcon={<DashboardIcon />}
          onClick={() => navigate('/dashboard')}
        >
          View Dashboard
        </Button>
        <Button
          variant="outlined"
          size="large"
          sx={{ mb: 2 }}
          startIcon={<ShippingIcon />}
          onClick={() => navigate('/tracking')}
        >
          Track Cargo
        </Button>
      </Box>
    </Container>
  )
}

// Dashboard Page Component
const DashboardPage: React.FC = () => {
  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Dashboard
        </Typography>
        <Typography variant="body1" paragraph>
          Welcome to your EXIM dashboard! Here you can monitor all your applications,
          track performance metrics, and get insights into your clearance operations.
        </Typography>
        
        <Grid container spacing={3} sx={{ mt: 2 }}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Recent Applications
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • APP-2024-001 - Import (Under Review)
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • APP-2024-002 - Export (Completed)
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • APP-2024-003 - Transit (Pending Payment)
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Performance Metrics
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • Average Processing Time: 3.2 days
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • Success Rate: 92.8%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • Customer Satisfaction: 4.6/5.0
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  System Status
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • All systems operational ✅
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • Payment gateway online ✅
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  • Customs API: Slow response ⚠️
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  )
}

// Applications Page Component
const ApplicationsPage: React.FC = () => {
  const [currentTab, setCurrentTab] = React.useState(0)

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue)
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4">
            Applications
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            size="large"
          >
            New Application
          </Button>
        </Box>

        <Tabs value={currentTab} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label="All Applications" />
          <Tab label="My Applications" />
          <Tab label="Pending Review" />
          <Tab label="Completed" />
        </Tabs>

        <Typography variant="body1" paragraph>
          Manage all your import/export applications in one place. Create new applications,
          track existing ones, and monitor their progress through the clearance process.
        </Typography>

        <Grid container spacing={2} sx={{ mt: 2 }}>
          {[
            { id: 'APP-2024-001', type: 'Import', status: 'Under Review', date: '2024-01-15' },
            { id: 'APP-2024-002', type: 'Export', status: 'Completed', date: '2024-01-14' },
            { id: 'APP-2024-003', type: 'Transit', status: 'Pending Payment', date: '2024-01-13' },
          ].map((app) => (
            <Grid item xs={12} key={app.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Typography variant="h6">{app.id}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {app.type} • Submitted: {app.date}
                      </Typography>
                    </Box>
                    <Chip 
                      label={app.status} 
                      color={app.status === 'Completed' ? 'success' : app.status === 'Under Review' ? 'warning' : 'info'}
                      variant="outlined"
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Paper>
    </Container>
  )
}

// New Application Page Component
const NewApplicationPage: React.FC = () => {
  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Create New Application
        </Typography>
        <Typography variant="body1" paragraph>
          Start a new import/export clearance application. Fill in the required information
          and upload necessary documents to begin the clearance process.
        </Typography>
        
        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            Application Type
          </Typography>
          <Grid container spacing={2}>
            {['Import', 'Export', 'Transit', 'Temporary Import', 'Re-export'].map((type) => (
              <Grid item xs={12} sm={6} md={4} key={type}>
                <Card sx={{ cursor: 'pointer', '&:hover': { bgcolor: 'action.hover' } }}>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <ApplicationsIcon sx={{ fontSize: 40, mb: 1, color: 'primary.main' }} />
                    <Typography variant="h6">{type}</Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Paper>
    </Container>
  )
}

// Tracking Page Component
const TrackingPage: React.FC = () => {
  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h4" gutterBottom>
          Cargo Tracking
        </Typography>
        <Typography variant="body1" paragraph>
          Track your cargo in real-time. Monitor the location, status, and estimated
          arrival times of your shipments throughout the clearance process.
        </Typography>
        
        <Grid container spacing={3} sx={{ mt: 2 }}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Active Shipments
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>Container: MSKU-123456</strong>
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Status: In Transit • Location: Lagos Port
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    ETA: 2024-01-20 14:30
                  </Typography>
                </Box>
                
                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>Container: TCLU-789012</strong>
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Status: Customs Clearance • Location: Apapa Port
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    ETA: 2024-01-18 09:15
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Tracking Features
                </Typography>
                <Box component="ul" sx={{ pl: 2 }}>
                  <li>Real-time GPS tracking</li>
                  <li>Container status monitoring</li>
                  <li>Temperature & humidity tracking</li>
                  <li>Geofencing alerts</li>
                  <li>ETA calculations</li>
                  <li>Historical tracking data</li>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  )
}

// Navigation Component
const Navigation: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()

  const getPageTitle = () => {
    switch (location.pathname) {
      case '/dashboard': return 'Dashboard'
      case '/applications': return 'Applications'
      case '/applications/new': return 'New Application'
      case '/tracking': return 'Cargo Tracking'
      default: return 'Home'
    }
  }

  return (
    <AppBar position="static">
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          EXIM Clearance System
        </Typography>
        <Button color="inherit" onClick={() => navigate('/')}>
          Home
        </Button>
        <Button color="inherit" onClick={() => navigate('/dashboard')}>
          Dashboard
        </Button>
        <Button color="inherit" onClick={() => navigate('/applications')}>
          Applications
        </Button>
        <Button color="inherit" onClick={() => navigate('/tracking')}>
          Tracking
        </Button>
      </Toolbar>
    </AppBar>
  )
}

// Breadcrumb Component
const BreadcrumbNav: React.FC = () => {
  const location = useLocation()
  const navigate = useNavigate()

  const pathnames = location.pathname.split('/').filter((x) => x)

  return (
    <Container maxWidth="lg" sx={{ mt: 2 }}>
      <Breadcrumbs aria-label="breadcrumb">
        <Link
          underline="hover"
          color="inherit"
          href="/"
          onClick={(e) => {
            e.preventDefault()
            navigate('/')
          }}
          sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
        >
          <HomeIcon sx={{ mr: 0.5 }} fontSize="inherit" />
          Home
        </Link>
        {pathnames.map((value, index) => {
          const last = index === pathnames.length - 1
          const to = `/${pathnames.slice(0, index + 1).join('/')}`

          return last ? (
            <Typography color="text.primary" key={to}>
              {value.charAt(0).toUpperCase() + value.slice(1)}
            </Typography>
          ) : (
            <Link
              underline="hover"
              color="inherit"
              key={to}
              onClick={(e) => {
                e.preventDefault()
                navigate(to)
              }}
              sx={{ cursor: 'pointer' }}
            >
              {value.charAt(0).toUpperCase() + value.slice(1)}
            </Link>
          )
        })}
      </Breadcrumbs>
    </Container>
  )
}

// Main App Component
const App: React.FC = () => {
  return (
    <Box sx={{ flexGrow: 1 }}>
      <Navigation />
      <BreadcrumbNav />
      
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/dashboard" element={<DashboardPage />} />
        <Route path="/applications" element={<ApplicationsPage />} />
        <Route path="/applications/new" element={<NewApplicationPage />} />
        <Route path="/tracking" element={<TrackingPage />} />
      </Routes>
    </Box>
  )
}

export default App
