import React from 'react'
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  Grid,
  AppBar,
  Toolbar,
  Container,
  Paper,
  Chip,
} from '@mui/material'
import {
  Dashboard as DashboardIcon,
  Description as ApplicationsIcon,
  Payment as PaymentIcon,
  LocalShipping as ShippingIcon,
} from '@mui/icons-material'

const SimpleApp: React.FC = () => {
  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Header */}
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            EXIM Clearance Certification System
          </Typography>
          <Button color="inherit">Login</Button>
        </Toolbar>
      </AppBar>

      {/* Main Content */}
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        {/* Welcome Section */}
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h3" gutterBottom>
            Welcome to EXIM Clearance System
          </Typography>
          <Typography variant="h6" color="text.secondary" paragraph>
            Advanced UI for Import/Export Clearance Operations
          </Typography>
          <Typography variant="body1" paragraph>
            This is a comprehensive platform for managing import/export clearance operations
            with multi-stakeholder support, real-time tracking, and advanced analytics.
          </Typography>
        </Paper>

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <DashboardIcon color="primary" sx={{ mr: 2, fontSize: 40 }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      1,247
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Applications
                    </Typography>
                  </Box>
                </Box>
                <Chip label="+12%" size="small" color="success" />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <ApplicationsIcon color="warning" sx={{ mr: 2, fontSize: 40 }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      89
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Pending Review
                    </Typography>
                  </Box>
                </Box>
                <Chip label="-5%" size="small" color="error" />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <ShippingIcon color="success" sx={{ mr: 2, fontSize: 40 }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      1,158
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Completed
                    </Typography>
                  </Box>
                </Box>
                <Chip label="+8%" size="small" color="success" />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <PaymentIcon color="info" sx={{ mr: 2, fontSize: 40 }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      ₦2.45M
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Revenue
                    </Typography>
                  </Box>
                </Box>
                <Chip label="+15%" size="small" color="success" />
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Features Section */}
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h5" gutterBottom>
                Key Features
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                <li>Multi-stakeholder portal system</li>
                <li>Advanced authentication with MFA</li>
                <li>Real-time cargo tracking</li>
                <li>AI-enhanced document management</li>
                <li>Payment gateway integration</li>
                <li>Comprehensive reporting</li>
                <li>Blockchain audit trails</li>
                <li>Mobile-responsive design</li>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h5" gutterBottom>
                Technology Stack
              </Typography>
              <Box component="ul" sx={{ pl: 2 }}>
                <li>React 18 with TypeScript</li>
                <li>Material-UI (MUI) components</li>
                <li>Framer Motion animations</li>
                <li>React Query for data fetching</li>
                <li>React Hook Form validation</li>
                <li>Recharts for visualization</li>
                <li>Docker containerization</li>
                <li>CI/CD with GitHub Actions</li>
              </Box>
            </Paper>
          </Grid>
        </Grid>

        {/* Action Buttons */}
        <Box sx={{ mt: 4, textAlign: 'center' }}>
          <Button
            variant="contained"
            size="large"
            sx={{ mr: 2, mb: 2 }}
            startIcon={<ApplicationsIcon />}
          >
            New Application
          </Button>
          <Button
            variant="outlined"
            size="large"
            sx={{ mr: 2, mb: 2 }}
            startIcon={<DashboardIcon />}
          >
            View Dashboard
          </Button>
          <Button
            variant="outlined"
            size="large"
            sx={{ mb: 2 }}
            startIcon={<ShippingIcon />}
          >
            Track Cargo
          </Button>
        </Box>
      </Container>
    </Box>
  )
}

export default SimpleApp
