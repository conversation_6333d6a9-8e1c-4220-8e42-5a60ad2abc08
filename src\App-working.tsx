import React from 'react'
import { Routes, Route, Navigate, useNavigate } from 'react-router-dom'
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  AppBar,
  Toolbar,
  Container,
  Paper,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  LinearProgress,
} from '@mui/material'
import {
  Dashboard as DashboardIcon,
  Description as ApplicationsIcon,
  Payment as PaymentIcon,
  LocalShipping as ShippingIcon,
  Add as AddIcon,
  Visibility as ViewIcon,
  MoreVert as MoreIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Business as BusinessIcon,
  CheckCircle as CompleteIcon,
  Save as SaveIcon,
  Send as SubmitIcon,
  Home as HomeIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'
import { toast } from 'react-toastify'

import { useApplications } from './contexts/ApplicationContext'
import { ApplicationType, Priority, ApplicationStatus } from './types'

// Home Page Component
const HomePage: React.FC = () => {
  const navigate = useNavigate()

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Paper sx={{ p: 3, mb: 4 }}>
          <Typography variant="h3" gutterBottom>
            Welcome to EXIM Clearance System
          </Typography>
          <Typography variant="h6" color="text.secondary" paragraph>
            Advanced UI for Import/Export Clearance Operations
          </Typography>
          <Typography variant="body1" paragraph>
            This is a comprehensive platform for managing import/export clearance operations
            with multi-stakeholder support, real-time tracking, and advanced analytics.
          </Typography>
        </Paper>

        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ cursor: 'pointer' }} onClick={() => navigate('/dashboard')}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <DashboardIcon color="primary" sx={{ mr: 2, fontSize: 40 }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      1,247
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Applications
                    </Typography>
                  </Box>
                </Box>
                <Chip label="+12%" size="small" color="success" />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ cursor: 'pointer' }} onClick={() => navigate('/applications')}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <ApplicationsIcon color="warning" sx={{ mr: 2, fontSize: 40 }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      89
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Pending Review
                    </Typography>
                  </Box>
                </Box>
                <Chip label="-5%" size="small" color="error" />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{ cursor: 'pointer' }} onClick={() => navigate('/tracking')}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <ShippingIcon color="success" sx={{ mr: 2, fontSize: 40 }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      1,158
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Completed
                    </Typography>
                  </Box>
                </Box>
                <Chip label="+8%" size="small" color="success" />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <PaymentIcon color="info" sx={{ mr: 2, fontSize: 40 }} />
                  <Box>
                    <Typography variant="h4" sx={{ fontWeight: 700 }}>
                      ₦2.45M
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total Revenue
                    </Typography>
                  </Box>
                </Box>
                <Chip label="+15%" size="small" color="success" />
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Box sx={{ mt: 4, textAlign: 'center' }}>
          <Button
            variant="contained"
            size="large"
            sx={{ mr: 2, mb: 2 }}
            startIcon={<AddIcon />}
            onClick={() => navigate('/applications/new')}
          >
            New Application
          </Button>
          <Button
            variant="outlined"
            size="large"
            sx={{ mr: 2, mb: 2 }}
            startIcon={<DashboardIcon />}
            onClick={() => navigate('/dashboard')}
          >
            View Dashboard
          </Button>
          <Button
            variant="outlined"
            size="large"
            sx={{ mb: 2 }}
            startIcon={<ShippingIcon />}
            onClick={() => navigate('/tracking')}
          >
            Track Cargo
          </Button>
        </Box>
      </motion.div>
    </Container>
  )
}

// Dashboard Page
const DashboardPage: React.FC = () => {
  const { applications } = useApplications()

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 600 }}>
          Dashboard
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Recent Applications
              </Typography>
              <List>
                {applications.slice(0, 5).map((app) => (
                  <ListItem key={app.id}>
                    <ListItemIcon>
                      <ApplicationsIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary={app.applicationNumber}
                      secondary={`${app.type} - ${app.status}`}
                    />
                    <Chip 
                      label={app.status} 
                      size="small" 
                      color={app.status === ApplicationStatus.COMPLETED ? 'success' : 'warning'}
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Quick Stats
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography>Total Applications:</Typography>
                  <Typography sx={{ fontWeight: 600 }}>{applications.length}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography>Completed:</Typography>
                  <Typography sx={{ fontWeight: 600 }}>
                    {applications.filter(app => app.status === ApplicationStatus.COMPLETED).length}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography>Pending:</Typography>
                  <Typography sx={{ fontWeight: 600 }}>
                    {applications.filter(app => app.status === ApplicationStatus.UNDER_REVIEW).length}
                  </Typography>
                </Box>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </motion.div>
    </Container>
  )
}

// Applications Page
const ApplicationsPage: React.FC = () => {
  const navigate = useNavigate()
  const { applications, deleteApplication } = useApplications()
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null)
  const [selectedApp, setSelectedApp] = React.useState<any>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = React.useState(false)

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, app: any) => {
    setAnchorEl(event.currentTarget)
    setSelectedApp(app)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setSelectedApp(null)
  }

  const handleDelete = async () => {
    if (selectedApp) {
      try {
        await deleteApplication(selectedApp.id)
        toast.success('Application deleted successfully')
      } catch (error) {
        toast.error('Failed to delete application')
      }
    }
    setDeleteDialogOpen(false)
    handleMenuClose()
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" sx={{ fontWeight: 600 }}>
            Applications ({applications.length})
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/applications/new')}
          >
            New Application
          </Button>
        </Box>

        <Grid container spacing={2}>
          {applications.map((app) => (
            <Grid item xs={12} key={app.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        {app.applicationNumber}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {app.applicant.organization} • {app.type}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Submitted: {app.submissionDate.toLocaleDateString()}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Chip 
                        label={app.status} 
                        color={app.status === ApplicationStatus.COMPLETED ? 'success' : 'warning'}
                        variant="outlined"
                      />
                      <Chip 
                        label={app.priority} 
                        size="small"
                        color={app.priority === Priority.URGENT ? 'error' : 'default'}
                      />
                      <IconButton onClick={(e) => handleMenuOpen(e, app)}>
                        <MoreIcon />
                      </IconButton>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleMenuClose}>
          <MenuItem onClick={() => { handleMenuClose(); navigate(`/applications/${selectedApp?.id}`); }}>
            <ViewIcon sx={{ mr: 1 }} />
            View Details
          </MenuItem>
          <MenuItem onClick={() => { handleMenuClose(); /* Edit functionality */ }}>
            <EditIcon sx={{ mr: 1 }} />
            Edit
          </MenuItem>
          <Divider />
          <MenuItem onClick={() => { handleMenuClose(); setDeleteDialogOpen(true); }} sx={{ color: 'error.main' }}>
            <DeleteIcon sx={{ mr: 1 }} />
            Delete
          </MenuItem>
        </Menu>

        <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
          <DialogTitle>Delete Application</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete {selectedApp?.applicationNumber}?
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleDelete} color="error" variant="contained">
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </motion.div>
    </Container>
  )
}

// Simple New Application Page
const NewApplicationPage: React.FC = () => {
  const navigate = useNavigate()
  const { createApplication } = useApplications()
  const [activeStep, setActiveStep] = React.useState(0)
  const [formData, setFormData] = React.useState({
    type: '',
    priority: '',
    companyName: '',
    contactPerson: '',
    email: '',
    cargoDescription: '',
    hsCode: '',
    quantity: 0,
    value: 0,
  })

  const handleSubmit = async () => {
    try {
      await createApplication({
        ...formData,
        type: formData.type as ApplicationType,
        priority: formData.priority as Priority,
        phone: '************',
        address: 'Sample Address',
        unit: 'pieces',
        weight: 100,
        currency: 'USD',
        origin: 'Sample Origin',
        destination: 'Sample Destination',
      })
      navigate('/applications')
    } catch (error) {
      toast.error('Failed to create application')
    }
  }

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 600 }}>
          Create New Application
        </Typography>

        <Paper sx={{ p: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Application Type</InputLabel>
                <Select
                  value={formData.type}
                  label="Application Type"
                  onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                >
                  <MenuItem value={ApplicationType.IMPORT}>Import</MenuItem>
                  <MenuItem value={ApplicationType.EXPORT}>Export</MenuItem>
                  <MenuItem value={ApplicationType.TRANSIT}>Transit</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Priority</InputLabel>
                <Select
                  value={formData.priority}
                  label="Priority"
                  onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
                >
                  <MenuItem value={Priority.LOW}>Low</MenuItem>
                  <MenuItem value={Priority.NORMAL}>Normal</MenuItem>
                  <MenuItem value={Priority.HIGH}>High</MenuItem>
                  <MenuItem value={Priority.URGENT}>Urgent</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Company Name"
                value={formData.companyName}
                onChange={(e) => setFormData({ ...formData, companyName: e.target.value })}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Contact Person"
                value={formData.contactPerson}
                onChange={(e) => setFormData({ ...formData, contactPerson: e.target.value })}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              />
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Cargo Description"
                multiline
                rows={3}
                value={formData.cargoDescription}
                onChange={(e) => setFormData({ ...formData, cargoDescription: e.target.value })}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="HS Code"
                value={formData.hsCode}
                onChange={(e) => setFormData({ ...formData, hsCode: e.target.value })}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Quantity"
                type="number"
                value={formData.quantity}
                onChange={(e) => setFormData({ ...formData, quantity: Number(e.target.value) })}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Value (USD)"
                type="number"
                value={formData.value}
                onChange={(e) => setFormData({ ...formData, value: Number(e.target.value) })}
              />
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                <Button variant="outlined" onClick={() => navigate('/applications')}>
                  Cancel
                </Button>
                <Button variant="contained" onClick={handleSubmit} startIcon={<SubmitIcon />}>
                  Submit Application
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </motion.div>
    </Container>
  )
}

// Navigation Component
const Navigation: React.FC = () => {
  const navigate = useNavigate()

  return (
    <AppBar position="static">
      <Toolbar>
        <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
          EXIM Clearance System
        </Typography>
        <Button color="inherit" onClick={() => navigate('/')}>
          <HomeIcon sx={{ mr: 1 }} />
          Home
        </Button>
        <Button color="inherit" onClick={() => navigate('/dashboard')}>
          <DashboardIcon sx={{ mr: 1 }} />
          Dashboard
        </Button>
        <Button color="inherit" onClick={() => navigate('/applications')}>
          <ApplicationsIcon sx={{ mr: 1 }} />
          Applications
        </Button>
      </Toolbar>
    </AppBar>
  )
}

// Main App Component
const App: React.FC = () => {
  return (
    <Box sx={{ flexGrow: 1 }}>
      <Navigation />
      
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/dashboard" element={<DashboardPage />} />
        <Route path="/applications" element={<ApplicationsPage />} />
        <Route path="/applications/new" element={<NewApplicationPage />} />
        <Route path="/applications/:id" element={<div>Application Details - Coming Soon</div>} />
        <Route path="/tracking" element={<div>Tracking Page - Coming Soon</div>} />
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Box>
  )
}

export default App
