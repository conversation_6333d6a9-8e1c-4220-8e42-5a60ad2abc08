import React, { Suspense } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Box, CircularProgress } from '@mui/material'
import { motion } from 'framer-motion'

import { useAuth } from './hooks/useAuth'
import Layout from './components/Layout/Layout'
import ProtectedRoute from './components/Auth/ProtectedRoute'
import ErrorBoundary from './components/Common/ErrorBoundary'

// Lazy load pages for better performance
const LoginPage = React.lazy(() => import('./pages/Auth/LoginPage'))
const DashboardPage = React.lazy(() => import('./pages/Dashboard/DashboardPage'))
const ApplicationsPage = React.lazy(() => import('./pages/Applications/ApplicationsPage'))
const DocumentsPage = React.lazy(() => import('./pages/Documents/DocumentsPage'))
const TrackingPage = React.lazy(() => import('./pages/Tracking/TrackingPage'))
const PaymentsPage = React.lazy(() => import('./pages/Payments/PaymentsPage'))
const ReportsPage = React.lazy(() => import('./pages/Reports/ReportsPage'))
const SettingsPage = React.lazy(() => import('./pages/Settings/SettingsPage'))
const NotFoundPage = React.lazy(() => import('./pages/Common/NotFoundPage'))

// Loading component
const LoadingFallback: React.FC = () => (
  <Box
    sx={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      flexDirection: 'column',
      gap: 2,
    }}
  >
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
    >
      <CircularProgress size={60} thickness={4} />
    </motion.div>
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.5 }}
    >
      Loading EXIM System...
    </motion.div>
  </Box>
)

const App: React.FC = () => {
  const { isAuthenticated, isLoading } = useAuth()

  if (isLoading) {
    return <LoadingFallback />
  }

  return (
    <ErrorBoundary>
      <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
        <Suspense fallback={<LoadingFallback />}>
          <Routes>
            {/* Public routes */}
            <Route
              path="/login"
              element={
                isAuthenticated ? (
                  <Navigate to="/dashboard" replace />
                ) : (
                  <LoginPage />
                )
              }
            />

            {/* Protected routes */}
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <Layout />
                </ProtectedRoute>
              }
            >
              <Route index element={<Navigate to="/dashboard" replace />} />
              <Route path="dashboard" element={<DashboardPage />} />
              <Route path="applications/*" element={<ApplicationsPage />} />
              <Route path="documents/*" element={<DocumentsPage />} />
              <Route path="tracking/*" element={<TrackingPage />} />
              <Route path="payments/*" element={<PaymentsPage />} />
              <Route path="reports/*" element={<ReportsPage />} />
              <Route path="settings/*" element={<SettingsPage />} />
            </Route>

            {/* Catch all route */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </Suspense>
      </Box>
    </ErrorBoundary>
  )
}

export default App
