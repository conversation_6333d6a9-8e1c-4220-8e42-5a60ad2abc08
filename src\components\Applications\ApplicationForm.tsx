import React, { useState } from 'react'
import {
  Box,
  <PERSON><PERSON>,
  Step,
  StepLabel,
  StepContent,
  Button,
  Typography,
  Paper,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Divider,
  Alert,
  Card,
  CardContent,
  useTheme,
} from '@mui/material'
import {
  Business as BusinessIcon,
  LocalShipping as ShippingIcon,
  AttachMoney as PaymentIcon,
  CheckCircle as CompleteIcon,
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'

import { ApplicationType, Priority } from '@/types'
import FileUpload from '@/components/Common/FileUpload'

// Validation schemas for each step
const basicInfoSchema = yup.object({
  applicationType: yup.string().required('Application type is required'),
  priority: yup.string().required('Priority is required'),
  companyName: yup.string().required('Company name is required'),
  contactPerson: yup.string().required('Contact person is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  phone: yup.string().required('Phone number is required'),
  address: yup.string().required('Address is required'),
})

const cargoDetailsSchema = yup.object({
  description: yup.string().required('Cargo description is required'),
  hsCode: yup.string().required('HS Code is required'),
  quantity: yup.number().positive('Quantity must be positive').required('Quantity is required'),
  unit: yup.string().required('Unit is required'),
  weight: yup.number().positive('Weight must be positive').required('Weight is required'),
  value: yup.number().positive('Value must be positive').required('Value is required'),
  currency: yup.string().required('Currency is required'),
  origin: yup.string().required('Origin is required'),
  destination: yup.string().required('Destination is required'),
})

const steps = [
  {
    label: 'Basic Information',
    description: 'Application type and company details',
    icon: <BusinessIcon />,
  },
  {
    label: 'Cargo Details',
    description: 'Cargo information and specifications',
    icon: <ShippingIcon />,
  },
  {
    label: 'Documents',
    description: 'Upload required documents',
    icon: <BusinessIcon />,
  },
  {
    label: 'Review & Submit',
    description: 'Review and submit application',
    icon: <CompleteIcon />,
  },
]

interface ApplicationFormProps {
  onSubmit: (data: any) => Promise<void>
  onSaveDraft?: (data: any) => Promise<void>
  initialData?: any
  loading?: boolean
}

const ApplicationForm: React.FC<ApplicationFormProps> = ({
  onSubmit,
  onSaveDraft,
  initialData,
  loading = false,
}) => {
  const theme = useTheme()
  const [activeStep, setActiveStep] = useState(0)
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])

  const {
    control: basicControl,
    handleSubmit: handleBasicSubmit,
    formState: { errors: basicErrors },
    watch: watchBasic,
  } = useForm({
    resolver: yupResolver(basicInfoSchema),
    defaultValues: initialData?.basicInfo || {},
  })

  const {
    control: cargoControl,
    handleSubmit: handleCargoSubmit,
    formState: { errors: cargoErrors },
    watch: watchCargo,
  } = useForm({
    resolver: yupResolver(cargoDetailsSchema),
    defaultValues: initialData?.cargoDetails || {},
  })

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1)
  }

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1)
  }

  const handleFilesUpload = async (files: File[]) => {
    setUploadedFiles((prev) => [...prev, ...files])
  }

  const handleFinalSubmit = async () => {
    const basicData = watchBasic()
    const cargoData = watchCargo()
    
    const applicationData = {
      basicInfo: basicData,
      cargoDetails: cargoData,
      documents: uploadedFiles,
    }

    await onSubmit(applicationData)
  }

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box component="form">
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Controller
                  name="applicationType"
                  control={basicControl}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!basicErrors.applicationType}>
                      <InputLabel>Application Type</InputLabel>
                      <Select {...field} label="Application Type">
                        <MenuItem value={ApplicationType.IMPORT}>Import</MenuItem>
                        <MenuItem value={ApplicationType.EXPORT}>Export</MenuItem>
                        <MenuItem value={ApplicationType.TRANSIT}>Transit</MenuItem>
                        <MenuItem value={ApplicationType.TEMPORARY_IMPORT}>Temporary Import</MenuItem>
                        <MenuItem value={ApplicationType.RE_EXPORT}>Re-export</MenuItem>
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="priority"
                  control={basicControl}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!basicErrors.priority}>
                      <InputLabel>Priority</InputLabel>
                      <Select {...field} label="Priority">
                        <MenuItem value={Priority.LOW}>Low</MenuItem>
                        <MenuItem value={Priority.NORMAL}>Normal</MenuItem>
                        <MenuItem value={Priority.HIGH}>High</MenuItem>
                        <MenuItem value={Priority.URGENT}>Urgent</MenuItem>
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="companyName"
                  control={basicControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Company Name"
                      error={!!basicErrors.companyName}
                      helperText={basicErrors.companyName?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="contactPerson"
                  control={basicControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Contact Person"
                      error={!!basicErrors.contactPerson}
                      helperText={basicErrors.contactPerson?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="email"
                  control={basicControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Email"
                      type="email"
                      error={!!basicErrors.email}
                      helperText={basicErrors.email?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="phone"
                  control={basicControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Phone Number"
                      error={!!basicErrors.phone}
                      helperText={basicErrors.phone?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="address"
                  control={basicControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Company Address"
                      multiline
                      rows={3}
                      error={!!basicErrors.address}
                      helperText={basicErrors.address?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Box>
        )

      case 1:
        return (
          <Box component="form">
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Controller
                  name="description"
                  control={cargoControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Cargo Description"
                      multiline
                      rows={3}
                      error={!!cargoErrors.description}
                      helperText={cargoErrors.description?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="hsCode"
                  control={cargoControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="HS Code"
                      error={!!cargoErrors.hsCode}
                      helperText={cargoErrors.hsCode?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={3}>
                <Controller
                  name="quantity"
                  control={cargoControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Quantity"
                      type="number"
                      error={!!cargoErrors.quantity}
                      helperText={cargoErrors.quantity?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={3}>
                <Controller
                  name="unit"
                  control={cargoControl}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!cargoErrors.unit}>
                      <InputLabel>Unit</InputLabel>
                      <Select {...field} label="Unit">
                        <MenuItem value="kg">Kilograms</MenuItem>
                        <MenuItem value="tons">Tons</MenuItem>
                        <MenuItem value="pieces">Pieces</MenuItem>
                        <MenuItem value="containers">Containers</MenuItem>
                        <MenuItem value="pallets">Pallets</MenuItem>
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <Controller
                  name="weight"
                  control={cargoControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Weight (kg)"
                      type="number"
                      error={!!cargoErrors.weight}
                      helperText={cargoErrors.weight?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <Controller
                  name="value"
                  control={cargoControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Value"
                      type="number"
                      error={!!cargoErrors.value}
                      helperText={cargoErrors.value?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <Controller
                  name="currency"
                  control={cargoControl}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!cargoErrors.currency}>
                      <InputLabel>Currency</InputLabel>
                      <Select {...field} label="Currency">
                        <MenuItem value="NGN">Nigerian Naira (NGN)</MenuItem>
                        <MenuItem value="USD">US Dollar (USD)</MenuItem>
                        <MenuItem value="EUR">Euro (EUR)</MenuItem>
                        <MenuItem value="GBP">British Pound (GBP)</MenuItem>
                      </Select>
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="origin"
                  control={cargoControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Origin Country"
                      error={!!cargoErrors.origin}
                      helperText={cargoErrors.origin?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="destination"
                  control={cargoControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Destination Country"
                      error={!!cargoErrors.destination}
                      helperText={cargoErrors.destination?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Box>
        )

      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Upload Required Documents
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Please upload all required documents for your application. Accepted formats: PDF, DOC, DOCX, JPG, PNG
            </Typography>
            
            <FileUpload
              onFilesUpload={handleFilesUpload}
              acceptedFileTypes={['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png']}
              maxFileSize={10 * 1024 * 1024} // 10MB
              maxFiles={10}
              helperText="Required documents may include: Commercial Invoice, Bill of Lading, Packing List, Certificate of Origin, etc."
            />
          </Box>
        )

      case 3:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review Your Application
            </Typography>
            
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                  Basic Information
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Type:</Typography>
                    <Typography variant="body2">{watchBasic().applicationType}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Priority:</Typography>
                    <Typography variant="body2">{watchBasic().priority}</Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">Company:</Typography>
                    <Typography variant="body2">{watchBasic().companyName}</Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                  Cargo Details
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">Description:</Typography>
                    <Typography variant="body2">{watchCargo().description}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">HS Code:</Typography>
                    <Typography variant="body2">{watchCargo().hsCode}</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Value:</Typography>
                    <Typography variant="body2">
                      {watchCargo().value} {watchCargo().currency}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>

            <Card>
              <CardContent>
                <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                  Documents ({uploadedFiles.length})
                </Typography>
                {uploadedFiles.map((file, index) => (
                  <Typography key={index} variant="body2">
                    • {file.name}
                  </Typography>
                ))}
              </CardContent>
            </Card>
          </Box>
        )

      default:
        return null
    }
  }

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, mb: 4 }}>
        New Application
      </Typography>

      <Stepper activeStep={activeStep} orientation="vertical">
        {steps.map((step, index) => (
          <Step key={step.label}>
            <StepLabel
              optional={
                index === steps.length - 1 ? (
                  <Typography variant="caption">Last step</Typography>
                ) : null
              }
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {step.icon}
                <Box>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                    {step.label}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {step.description}
                  </Typography>
                </Box>
              </Box>
            </StepLabel>
            <StepContent>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Box sx={{ mb: 3 }}>
                  {renderStepContent(index)}
                </Box>
                
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Button
                    disabled={index === 0}
                    onClick={handleBack}
                    variant="outlined"
                  >
                    Back
                  </Button>
                  
                  {index === steps.length - 1 ? (
                    <Button
                      variant="contained"
                      onClick={handleFinalSubmit}
                      disabled={loading}
                    >
                      {loading ? 'Submitting...' : 'Submit Application'}
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      onClick={() => {
                        if (index === 0) {
                          handleBasicSubmit(handleNext)()
                        } else if (index === 1) {
                          handleCargoSubmit(handleNext)()
                        } else {
                          handleNext()
                        }
                      }}
                    >
                      Continue
                    </Button>
                  )}
                  
                  {onSaveDraft && (
                    <Button
                      variant="text"
                      onClick={() => {
                        const data = {
                          basicInfo: watchBasic(),
                          cargoDetails: watchCargo(),
                          documents: uploadedFiles,
                        }
                        onSaveDraft(data)
                      }}
                    >
                      Save Draft
                    </Button>
                  )}
                </Box>
              </motion.div>
            </StepContent>
          </Step>
        ))}
      </Stepper>
    </Box>
  )
}

export default ApplicationForm
