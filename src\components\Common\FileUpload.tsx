import React, { useCallback, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import {
  Box,
  Typography,
  Paper,
  LinearProgress,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Chip,
  Alert,
  useTheme,
} from '@mui/material'
import {
  CloudUpload as UploadIcon,
  InsertDriveFile as FileIcon,
  Delete as DeleteIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'

interface UploadedFile {
  file: File
  id: string
  progress: number
  status: 'uploading' | 'success' | 'error'
  error?: string
}

interface FileUploadProps {
  onFilesUpload: (files: File[]) => Promise<void>
  acceptedFileTypes?: string[]
  maxFileSize?: number
  maxFiles?: number
  multiple?: boolean
  disabled?: boolean
  helperText?: string
}

const FileUpload: React.FC<FileUploadProps> = ({
  onFilesUpload,
  acceptedFileTypes = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'],
  maxFileSize = 10 * 1024 * 1024, // 10MB
  maxFiles = 5,
  multiple = true,
  disabled = false,
  helperText,
}) => {
  const theme = useTheme()
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [error, setError] = useState<string | null>(null)

  const onDrop = useCallback(
    async (acceptedFiles: File[], rejectedFiles: any[]) => {
      setError(null)

      // Handle rejected files
      if (rejectedFiles.length > 0) {
        const errors = rejectedFiles.map((rejection) => {
          const { file, errors } = rejection
          return `${file.name}: ${errors.map((e: any) => e.message).join(', ')}`
        })
        setError(errors.join('\n'))
        return
      }

      // Check file count limit
      if (uploadedFiles.length + acceptedFiles.length > maxFiles) {
        setError(`Maximum ${maxFiles} files allowed`)
        return
      }

      // Create upload entries
      const newFiles: UploadedFile[] = acceptedFiles.map((file) => ({
        file,
        id: `${file.name}-${Date.now()}`,
        progress: 0,
        status: 'uploading',
      }))

      setUploadedFiles((prev) => [...prev, ...newFiles])

      try {
        // Simulate upload progress
        for (const uploadFile of newFiles) {
          // Update progress
          for (let progress = 0; progress <= 100; progress += 10) {
            await new Promise((resolve) => setTimeout(resolve, 100))
            setUploadedFiles((prev) =>
              prev.map((f) =>
                f.id === uploadFile.id ? { ...f, progress } : f
              )
            )
          }

          // Mark as success
          setUploadedFiles((prev) =>
            prev.map((f) =>
              f.id === uploadFile.id ? { ...f, status: 'success' } : f
            )
          )
        }

        // Call the upload handler
        await onFilesUpload(acceptedFiles)
      } catch (error: any) {
        // Mark files as error
        setUploadedFiles((prev) =>
          prev.map((f) =>
            newFiles.some((nf) => nf.id === f.id)
              ? { ...f, status: 'error', error: error.message }
              : f
          )
        )
      }
    },
    [uploadedFiles.length, maxFiles, onFilesUpload]
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes.reduce((acc, type) => {
      acc[`.${type}`] = []
      return acc
    }, {} as Record<string, string[]>),
    maxSize: maxFileSize,
    multiple,
    disabled,
  })

  const removeFile = (fileId: string) => {
    setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId))
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <Box>
      {/* Upload Area */}
      <Paper
        {...getRootProps()}
        sx={{
          p: 4,
          border: `2px dashed ${
            isDragActive
              ? theme.palette.primary.main
              : theme.palette.divider
          }`,
          borderRadius: 2,
          backgroundColor: isDragActive
            ? theme.palette.primary.light + '10'
            : theme.palette.background.default,
          cursor: disabled ? 'not-allowed' : 'pointer',
          transition: 'all 0.3s ease',
          textAlign: 'center',
          '&:hover': {
            borderColor: disabled ? theme.palette.divider : theme.palette.primary.main,
            backgroundColor: disabled
              ? theme.palette.background.default
              : theme.palette.primary.light + '05',
          },
        }}
      >
        <input {...getInputProps()} />
        
        <motion.div
          animate={{
            scale: isDragActive ? 1.05 : 1,
          }}
          transition={{ duration: 0.2 }}
        >
          <UploadIcon
            sx={{
              fontSize: 48,
              color: isDragActive
                ? theme.palette.primary.main
                : theme.palette.text.secondary,
              mb: 2,
            }}
          />
          
          <Typography variant="h6" gutterBottom>
            {isDragActive
              ? 'Drop files here'
              : 'Drag & drop files here, or click to select'}
          </Typography>
          
          <Typography variant="body2" color="text.secondary" paragraph>
            Accepted formats: {acceptedFileTypes.join(', ').toUpperCase()}
          </Typography>
          
          <Typography variant="caption" color="text.secondary">
            Max file size: {formatFileSize(maxFileSize)} • Max files: {maxFiles}
          </Typography>
          
          {helperText && (
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              {helperText}
            </Typography>
          )}
        </motion.div>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mt: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <Paper sx={{ mt: 2 }}>
          <List>
            <AnimatePresence>
              {uploadedFiles.map((uploadFile) => (
                <motion.div
                  key={uploadFile.id}
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <ListItem>
                    <ListItemIcon>
                      {uploadFile.status === 'success' ? (
                        <SuccessIcon color="success" />
                      ) : uploadFile.status === 'error' ? (
                        <ErrorIcon color="error" />
                      ) : (
                        <FileIcon />
                      )}
                    </ListItemIcon>
                    
                    <ListItemText
                      primary={uploadFile.file.name}
                      secondary={
                        <Box>
                          <Typography variant="caption" color="text.secondary">
                            {formatFileSize(uploadFile.file.size)}
                          </Typography>
                          
                          {uploadFile.status === 'uploading' && (
                            <LinearProgress
                              variant="determinate"
                              value={uploadFile.progress}
                              sx={{ mt: 1 }}
                            />
                          )}
                          
                          {uploadFile.status === 'error' && uploadFile.error && (
                            <Typography variant="caption" color="error">
                              {uploadFile.error}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                    
                    <ListItemSecondaryAction>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip
                          label={uploadFile.status}
                          size="small"
                          color={
                            uploadFile.status === 'success'
                              ? 'success'
                              : uploadFile.status === 'error'
                              ? 'error'
                              : 'default'
                          }
                          variant="outlined"
                        />
                        
                        <IconButton
                          edge="end"
                          onClick={() => removeFile(uploadFile.id)}
                          size="small"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </ListItemSecondaryAction>
                  </ListItem>
                </motion.div>
              ))}
            </AnimatePresence>
          </List>
        </Paper>
      )}
    </Box>
  )
}

export default FileUpload
