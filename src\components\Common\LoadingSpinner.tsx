import React from 'react'
import {
  Box,
  CircularProgress,
  Typography,
  Backdrop,
  useTheme,
} from '@mui/material'
import { motion } from 'framer-motion'

interface LoadingSpinnerProps {
  size?: number
  message?: string
  overlay?: boolean
  color?: 'primary' | 'secondary' | 'inherit'
  thickness?: number
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 40,
  message,
  overlay = false,
  color = 'primary',
  thickness = 4,
}) => {
  const theme = useTheme()

  const spinnerContent = (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 2,
        p: 2,
      }}
    >
      <motion.div
        animate={{ rotate: 360 }}
        transition={{
          duration: 1,
          repeat: Infinity,
          ease: 'linear',
        }}
      >
        <CircularProgress
          size={size}
          thickness={thickness}
          color={color}
        />
      </motion.div>
      
      {message && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <Typography
            variant="body2"
            color="text.secondary"
            align="center"
            sx={{ maxWidth: 200 }}
          >
            {message}
          </Typography>
        </motion.div>
      )}
    </Box>
  )

  if (overlay) {
    return (
      <Backdrop
        open={true}
        sx={{
          color: '#fff',
          zIndex: theme.zIndex.modal + 1,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          backdropFilter: 'blur(4px)',
        }}
      >
        {spinnerContent}
      </Backdrop>
    )
  }

  return spinnerContent
}

export default LoadingSpinner
