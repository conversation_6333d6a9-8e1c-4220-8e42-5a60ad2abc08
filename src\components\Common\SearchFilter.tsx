import React, { useState } from 'react'
import {
  Box,
  TextField,
  InputAdornment,
  IconButton,
  Menu,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Chip,
  Button,
  Paper,
  Typography,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  FormControlLabel,
  Checkbox,
  Slider,
  useTheme,
} from '@mui/material'
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Clear as ClearIcon,
  ExpandMore as ExpandMoreIcon,
  CalendarToday as DateIcon,
} from '@mui/icons-material'
import { DatePicker } from '@mui/x-date-pickers/DatePicker'
import { motion, AnimatePresence } from 'framer-motion'

import { FilterOptions, SortOptions } from '@/types'

interface FilterConfig {
  id: string
  label: string
  type: 'select' | 'multiselect' | 'date' | 'daterange' | 'number' | 'checkbox'
  options?: { label: string; value: any }[]
  min?: number
  max?: number
}

interface SearchFilterProps {
  searchValue: string
  onSearchChange: (value: string) => void
  filters?: FilterConfig[]
  activeFilters: Record<string, any>
  onFiltersChange: (filters: Record<string, any>) => void
  sortOptions?: { label: string; value: string }[]
  sortValue?: SortOptions
  onSortChange?: (sort: SortOptions) => void
  onClearAll?: () => void
  showFilterCount?: boolean
}

const SearchFilter: React.FC<SearchFilterProps> = ({
  searchValue,
  onSearchChange,
  filters = [],
  activeFilters,
  onFiltersChange,
  sortOptions = [],
  sortValue,
  onSortChange,
  onClearAll,
  showFilterCount = true,
}) => {
  const theme = useTheme()
  const [filterMenuOpen, setFilterMenuOpen] = useState(false)
  const [filterAnchorEl, setFilterAnchorEl] = useState<null | HTMLElement>(null)

  const activeFilterCount = Object.keys(activeFilters).filter(
    (key) => activeFilters[key] !== undefined && activeFilters[key] !== null && activeFilters[key] !== ''
  ).length

  const handleFilterChange = (filterId: string, value: any) => {
    onFiltersChange({
      ...activeFilters,
      [filterId]: value,
    })
  }

  const handleClearFilter = (filterId: string) => {
    const newFilters = { ...activeFilters }
    delete newFilters[filterId]
    onFiltersChange(newFilters)
  }

  const renderFilterControl = (filter: FilterConfig) => {
    const value = activeFilters[filter.id]

    switch (filter.type) {
      case 'select':
        return (
          <FormControl fullWidth size="small" sx={{ mb: 2 }}>
            <InputLabel>{filter.label}</InputLabel>
            <Select
              value={value || ''}
              label={filter.label}
              onChange={(e) => handleFilterChange(filter.id, e.target.value)}
            >
              <MenuItem value="">
                <em>All</em>
              </MenuItem>
              {filter.options?.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )

      case 'multiselect':
        return (
          <FormControl fullWidth size="small" sx={{ mb: 2 }}>
            <InputLabel>{filter.label}</InputLabel>
            <Select
              multiple
              value={value || []}
              label={filter.label}
              onChange={(e) => handleFilterChange(filter.id, e.target.value)}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {(selected as string[]).map((val) => {
                    const option = filter.options?.find((opt) => opt.value === val)
                    return (
                      <Chip
                        key={val}
                        label={option?.label || val}
                        size="small"
                        onDelete={() => {
                          const newValue = (value || []).filter((v: any) => v !== val)
                          handleFilterChange(filter.id, newValue)
                        }}
                      />
                    )
                  })}
                </Box>
              )}
            >
              {filter.options?.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  <Checkbox checked={(value || []).indexOf(option.value) > -1} />
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )

      case 'date':
        return (
          <DatePicker
            label={filter.label}
            value={value || null}
            onChange={(newValue) => handleFilterChange(filter.id, newValue)}
            slotProps={{
              textField: {
                fullWidth: true,
                size: 'small',
                sx: { mb: 2 },
              },
            }}
          />
        )

      case 'daterange':
        return (
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              {filter.label}
            </Typography>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <DatePicker
                label="From"
                value={value?.start || null}
                onChange={(newValue) =>
                  handleFilterChange(filter.id, { ...value, start: newValue })
                }
                slotProps={{
                  textField: { size: 'small' },
                }}
              />
              <DatePicker
                label="To"
                value={value?.end || null}
                onChange={(newValue) =>
                  handleFilterChange(filter.id, { ...value, end: newValue })
                }
                slotProps={{
                  textField: { size: 'small' },
                }}
              />
            </Box>
          </Box>
        )

      case 'number':
        return (
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              {filter.label}
            </Typography>
            <Slider
              value={value || [filter.min || 0, filter.max || 100]}
              onChange={(_, newValue) => handleFilterChange(filter.id, newValue)}
              valueLabelDisplay="auto"
              min={filter.min || 0}
              max={filter.max || 100}
            />
          </Box>
        )

      case 'checkbox':
        return (
          <FormControlLabel
            control={
              <Checkbox
                checked={value || false}
                onChange={(e) => handleFilterChange(filter.id, e.target.checked)}
              />
            }
            label={filter.label}
            sx={{ mb: 1 }}
          />
        )

      default:
        return null
    }
  }

  return (
    <Box sx={{ mb: 3 }}>
      {/* Search and Filter Bar */}
      <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', mb: 2 }}>
        {/* Search Field */}
        <TextField
          fullWidth
          placeholder="Search..."
          value={searchValue}
          onChange={(e) => onSearchChange(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            endAdornment: searchValue && (
              <InputAdornment position="end">
                <IconButton
                  size="small"
                  onClick={() => onSearchChange('')}
                >
                  <ClearIcon />
                </IconButton>
              </InputAdornment>
            ),
          }}
        />

        {/* Filter Button */}
        {filters.length > 0 && (
          <Button
            variant="outlined"
            startIcon={<FilterIcon />}
            onClick={(e) => {
              setFilterAnchorEl(e.currentTarget)
              setFilterMenuOpen(true)
            }}
            sx={{ minWidth: 120 }}
          >
            Filters
            {showFilterCount && activeFilterCount > 0 && (
              <Chip
                label={activeFilterCount}
                size="small"
                color="primary"
                sx={{ ml: 1 }}
              />
            )}
          </Button>
        )}

        {/* Sort Dropdown */}
        {sortOptions.length > 0 && (
          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>Sort by</InputLabel>
            <Select
              value={sortValue?.field || ''}
              label="Sort by"
              onChange={(e) => {
                const field = e.target.value as string
                onSortChange?.({
                  field,
                  direction: sortValue?.direction || 'asc',
                })
              }}
            >
              {sortOptions.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}

        {/* Clear All Button */}
        {(activeFilterCount > 0 || searchValue) && (
          <Button
            variant="text"
            color="secondary"
            onClick={onClearAll}
          >
            Clear All
          </Button>
        )}
      </Box>

      {/* Active Filters Display */}
      {activeFilterCount > 0 && (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          {Object.entries(activeFilters).map(([key, value]) => {
            if (!value || (Array.isArray(value) && value.length === 0)) return null
            
            const filter = filters.find((f) => f.id === key)
            if (!filter) return null

            let displayValue = value
            if (Array.isArray(value)) {
              displayValue = value.map((v) => {
                const option = filter.options?.find((opt) => opt.value === v)
                return option?.label || v
              }).join(', ')
            } else if (filter.options) {
              const option = filter.options.find((opt) => opt.value === value)
              displayValue = option?.label || value
            }

            return (
              <Chip
                key={key}
                label={`${filter.label}: ${displayValue}`}
                onDelete={() => handleClearFilter(key)}
                size="small"
                variant="outlined"
              />
            )
          })}
        </Box>
      )}

      {/* Filter Menu */}
      <Menu
        anchorEl={filterAnchorEl}
        open={filterMenuOpen}
        onClose={() => {
          setFilterMenuOpen(false)
          setFilterAnchorEl(null)
        }}
        PaperProps={{
          sx: { width: 350, maxHeight: 500 },
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Filters
          </Typography>
          <Divider sx={{ mb: 2 }} />
          
          {filters.map((filter) => (
            <Box key={filter.id}>
              {renderFilterControl(filter)}
            </Box>
          ))}
          
          <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
            <Button
              variant="outlined"
              size="small"
              onClick={() => {
                onFiltersChange({})
                setFilterMenuOpen(false)
              }}
            >
              Clear All
            </Button>
            <Button
              variant="contained"
              size="small"
              onClick={() => setFilterMenuOpen(false)}
            >
              Apply
            </Button>
          </Box>
        </Box>
      </Menu>
    </Box>
  )
}

export default SearchFilter
