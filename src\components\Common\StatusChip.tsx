import React from 'react'
import { Chip, ChipProps, useTheme } from '@mui/material'
import {
  CheckCircle as CompletedIcon,
  Schedule as PendingIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Cancel as CancelledIcon,
  PlayArrow as InProgressIcon,
} from '@mui/icons-material'

import { ApplicationStatus, PaymentStatus, DocumentStatus, Priority } from '@/types'

interface StatusChipProps extends Omit<ChipProps, 'color'> {
  status: ApplicationStatus | PaymentStatus | DocumentStatus | Priority | string
  variant?: 'filled' | 'outlined'
  showIcon?: boolean
}

const StatusChip: React.FC<StatusChipProps> = ({
  status,
  variant = 'filled',
  showIcon = true,
  ...props
}) => {
  const theme = useTheme()

  const getStatusConfig = (status: string) => {
    const statusLower = status.toLowerCase()

    // Application Status
    if (statusLower === 'completed' || statusLower === 'approved') {
      return {
        color: 'success' as const,
        icon: <CompletedIcon />,
        label: 'Completed',
      }
    }
    
    if (statusLower === 'pending' || statusLower === 'submitted' || statusLower === 'under_review') {
      return {
        color: 'warning' as const,
        icon: <PendingIcon />,
        label: 'Pending',
      }
    }
    
    if (statusLower === 'in_progress' || statusLower === 'processing') {
      return {
        color: 'info' as const,
        icon: <InProgressIcon />,
        label: 'In Progress',
      }
    }
    
    if (statusLower === 'rejected' || statusLower === 'failed' || statusLower === 'invalid') {
      return {
        color: 'error' as const,
        icon: <ErrorIcon />,
        label: 'Rejected',
      }
    }
    
    if (statusLower === 'cancelled' || statusLower === 'expired') {
      return {
        color: 'default' as const,
        icon: <CancelledIcon />,
        label: 'Cancelled',
      }
    }

    // Priority Status
    if (statusLower === 'high' || statusLower === 'urgent') {
      return {
        color: 'error' as const,
        icon: <WarningIcon />,
        label: 'High Priority',
      }
    }
    
    if (statusLower === 'normal' || statusLower === 'medium') {
      return {
        color: 'info' as const,
        icon: <InfoIcon />,
        label: 'Normal',
      }
    }
    
    if (statusLower === 'low') {
      return {
        color: 'default' as const,
        icon: <InfoIcon />,
        label: 'Low Priority',
      }
    }

    // Payment Status
    if (statusLower === 'paid' || statusLower === 'payment_confirmed') {
      return {
        color: 'success' as const,
        icon: <CompletedIcon />,
        label: 'Paid',
      }
    }
    
    if (statusLower === 'pending_payment') {
      return {
        color: 'warning' as const,
        icon: <PendingIcon />,
        label: 'Pending Payment',
      }
    }

    // Document Status
    if (statusLower === 'valid' || statusLower === 'verified') {
      return {
        color: 'success' as const,
        icon: <CompletedIcon />,
        label: 'Valid',
      }
    }
    
    if (statusLower === 'validating' || statusLower === 'uploaded') {
      return {
        color: 'info' as const,
        icon: <PendingIcon />,
        label: 'Validating',
      }
    }

    // Default case
    return {
      color: 'default' as const,
      icon: <InfoIcon />,
      label: status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    }
  }

  const config = getStatusConfig(status)

  return (
    <Chip
      {...props}
      label={config.label}
      color={config.color}
      variant={variant}
      icon={showIcon ? config.icon : undefined}
      size="small"
      sx={{
        fontWeight: 500,
        '& .MuiChip-icon': {
          fontSize: '1rem',
        },
        ...props.sx,
      }}
    />
  )
}

export default StatusChip
