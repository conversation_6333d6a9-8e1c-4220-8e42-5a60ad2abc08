import React from 'react'
import {
  <PERSON>,
  CardContent,
  CardHeader,
  Typography,
  Box,
  IconButton,
  Menu,
  MenuItem,
  useTheme,
} from '@mui/material'
import {
  MoreVert as MoreIcon,
  GetApp as ExportIcon,
  Refresh as RefreshIcon,
  Fullscreen as FullscreenIcon,
} from '@mui/icons-material'
import {
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
} from 'recharts'
import { motion } from 'framer-motion'

interface ChartData {
  [key: string]: any
}

interface ChartCardProps {
  title: string
  subtitle?: string
  type: 'line' | 'area' | 'bar' | 'pie'
  data: ChartData[]
  dataKey?: string
  xAxisKey?: string
  height?: number
  colors?: string[]
  onExport?: () => void
  onRefresh?: () => void
  onFullscreen?: () => void
  loading?: boolean
}

const ChartCard: React.FC<ChartCardProps> = ({
  title,
  subtitle,
  type,
  data,
  dataKey = 'value',
  xAxisKey = 'name',
  height = 300,
  colors,
  onExport,
  onRefresh,
  onFullscreen,
  loading = false,
}) => {
  const theme = useTheme()
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null)

  const defaultColors = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
    theme.palette.info.main,
  ]

  const chartColors = colors || defaultColors

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
  }

  const renderChart = () => {
    const commonProps = {
      data,
      margin: { top: 5, right: 30, left: 20, bottom: 5 },
    }

    switch (type) {
      case 'line':
        return (
          <LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
            <XAxis
              dataKey={xAxisKey}
              stroke={theme.palette.text.secondary}
              fontSize={12}
            />
            <YAxis stroke={theme.palette.text.secondary} fontSize={12} />
            <Tooltip
              contentStyle={{
                backgroundColor: theme.palette.background.paper,
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: theme.shape.borderRadius,
              }}
            />
            <Legend />
            <Line
              type="monotone"
              dataKey={dataKey}
              stroke={chartColors[0]}
              strokeWidth={2}
              dot={{ fill: chartColors[0], strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: chartColors[0], strokeWidth: 2 }}
            />
          </LineChart>
        )

      case 'area':
        return (
          <AreaChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
            <XAxis
              dataKey={xAxisKey}
              stroke={theme.palette.text.secondary}
              fontSize={12}
            />
            <YAxis stroke={theme.palette.text.secondary} fontSize={12} />
            <Tooltip
              contentStyle={{
                backgroundColor: theme.palette.background.paper,
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: theme.shape.borderRadius,
              }}
            />
            <Area
              type="monotone"
              dataKey={dataKey}
              stroke={chartColors[0]}
              fill={`${chartColors[0]}30`}
              strokeWidth={2}
            />
          </AreaChart>
        )

      case 'bar':
        return (
          <BarChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
            <XAxis
              dataKey={xAxisKey}
              stroke={theme.palette.text.secondary}
              fontSize={12}
            />
            <YAxis stroke={theme.palette.text.secondary} fontSize={12} />
            <Tooltip
              contentStyle={{
                backgroundColor: theme.palette.background.paper,
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: theme.shape.borderRadius,
              }}
            />
            <Bar dataKey={dataKey} fill={chartColors[0]} radius={[4, 4, 0, 0]} />
          </BarChart>
        )

      case 'pie':
        return (
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey={dataKey}
            >
              {data.map((_, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={chartColors[index % chartColors.length]}
                />
              ))}
            </Pie>
            <Tooltip
              contentStyle={{
                backgroundColor: theme.palette.background.paper,
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: theme.shape.borderRadius,
              }}
            />
          </PieChart>
        )

      default:
        return null
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card sx={{ height: '100%' }}>
        <CardHeader
          title={
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              {title}
            </Typography>
          }
          subheader={subtitle}
          action={
            <IconButton onClick={handleMenuOpen}>
              <MoreIcon />
            </IconButton>
          }
        />
        
        <CardContent sx={{ pt: 0 }}>
          <Box sx={{ width: '100%', height }}>
            {loading ? (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%',
                }}
              >
                <Typography variant="body2" color="text.secondary">
                  Loading chart data...
                </Typography>
              </Box>
            ) : data.length === 0 ? (
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%',
                }}
              >
                <Typography variant="body2" color="text.secondary">
                  No data available
                </Typography>
              </Box>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                {renderChart()}
              </ResponsiveContainer>
            )}
          </Box>
        </CardContent>

        {/* Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          {onRefresh && (
            <MenuItem
              onClick={() => {
                onRefresh()
                handleMenuClose()
              }}
            >
              <RefreshIcon sx={{ mr: 1 }} />
              Refresh
            </MenuItem>
          )}
          
          {onExport && (
            <MenuItem
              onClick={() => {
                onExport()
                handleMenuClose()
              }}
            >
              <ExportIcon sx={{ mr: 1 }} />
              Export
            </MenuItem>
          )}
          
          {onFullscreen && (
            <MenuItem
              onClick={() => {
                onFullscreen()
                handleMenuClose()
              }}
            >
              <FullscreenIcon sx={{ mr: 1 }} />
              Fullscreen
            </MenuItem>
          )}
        </Menu>
      </Card>
    </motion.div>
  )
}

export default ChartCard
