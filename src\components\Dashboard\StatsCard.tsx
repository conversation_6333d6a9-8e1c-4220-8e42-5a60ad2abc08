import React from 'react'
import {
  Card,
  CardContent,
  Typography,
  Box,
  Avatar,
  Chip,
  LinearProgress,
  useTheme,
} from '@mui/material'
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'

interface StatsCardProps {
  title: string
  value: string | number
  icon: React.ReactNode
  color?: string
  trend?: {
    value: number
    label: string
    isPositive?: boolean
  }
  progress?: {
    value: number
    label: string
  }
  subtitle?: string
  onClick?: () => void
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon,
  color,
  trend,
  progress,
  subtitle,
  onClick,
}) => {
  const theme = useTheme()
  const cardColor = color || theme.palette.primary.main

  return (
    <motion.div
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
    >
      <Card
        sx={{
          height: '100%',
          background: `linear-gradient(135deg, ${cardColor}15 0%, ${cardColor}05 100%)`,
          border: `1px solid ${cardColor}20`,
          cursor: onClick ? 'pointer' : 'default',
          '&:hover': onClick ? {
            boxShadow: theme.shadows[8],
          } : {},
          transition: 'all 0.3s ease-in-out',
        }}
        onClick={onClick}
      >
        <CardContent sx={{ p: 3 }}>
          {/* Header */}
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar
              sx={{
                bgcolor: cardColor,
                width: 56,
                height: 56,
                mr: 2,
              }}
            >
              {icon}
            </Avatar>
            <Box sx={{ flexGrow: 1 }}>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 700,
                  color: theme.palette.text.primary,
                  lineHeight: 1,
                }}
              >
                {typeof value === 'number' ? value.toLocaleString() : value}
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ mt: 0.5 }}
              >
                {title}
              </Typography>
            </Box>
          </Box>

          {/* Subtitle */}
          {subtitle && (
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ mb: 2, display: 'block' }}
            >
              {subtitle}
            </Typography>
          )}

          {/* Trend */}
          {trend && (
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Chip
                icon={
                  trend.isPositive !== false ? (
                    <TrendingUpIcon />
                  ) : (
                    <TrendingDownIcon />
                  )
                }
                label={`${trend.value > 0 ? '+' : ''}${trend.value}%`}
                size="small"
                color={trend.isPositive !== false ? 'success' : 'error'}
                variant="outlined"
                sx={{ mr: 1 }}
              />
              <Typography variant="caption" color="text.secondary">
                {trend.label}
              </Typography>
            </Box>
          )}

          {/* Progress */}
          {progress && (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="caption" color="text.secondary">
                  {progress.label}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {progress.value}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={progress.value}
                sx={{
                  height: 6,
                  borderRadius: 3,
                  backgroundColor: theme.palette.grey[200],
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 3,
                    backgroundColor: cardColor,
                  },
                }}
              />
            </Box>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}

export default StatsCard
