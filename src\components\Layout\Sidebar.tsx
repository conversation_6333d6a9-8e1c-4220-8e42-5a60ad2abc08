import React from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Divider,
  Tooltip,
  useTheme,
  alpha,
} from '@mui/material'
import {
  Dashboard as DashboardIcon,
  Description as ApplicationsIcon,
  Folder as DocumentsIcon,
  LocalShipping as TrackingIcon,
  Payment as PaymentsIcon,
  Assessment as ReportsIcon,
  Settings as SettingsIcon,
  Business as CustomsIcon,
  DirectionsBoat as ShippingIcon,
  LocationOn as PortIcon,
  AdminPanelSettings as AdminIcon,
  Analytics as AnalyticsIcon,
  Security as ComplianceIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'

import { useAuth } from '@/hooks/useAuth'
import { UserRole } from '@/types'

interface SidebarProps {
  collapsed: boolean
  onItemClick?: () => void
}

interface NavigationItem {
  id: string
  label: string
  icon: React.ReactNode
  path: string
  roles?: UserRole[]
  badge?: string | number
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: <DashboardIcon />,
    path: '/dashboard',
  },
  {
    id: 'applications',
    label: 'Applications',
    icon: <ApplicationsIcon />,
    path: '/applications',
  },
  {
    id: 'documents',
    label: 'Documents',
    icon: <DocumentsIcon />,
    path: '/documents',
  },
  {
    id: 'tracking',
    label: 'Cargo Tracking',
    icon: <TrackingIcon />,
    path: '/tracking',
  },
  {
    id: 'payments',
    label: 'Payments',
    icon: <PaymentsIcon />,
    path: '/payments',
  },
  {
    id: 'customs',
    label: 'Customs Portal',
    icon: <CustomsIcon />,
    path: '/customs',
    roles: [UserRole.CUSTOMS_OFFICER, UserRole.ADMIN, UserRole.SUPER_ADMIN],
  },
  {
    id: 'shipping',
    label: 'Shipping Portal',
    icon: <ShippingIcon />,
    path: '/shipping',
    roles: [UserRole.SHIPPING_AGENT, UserRole.ADMIN, UserRole.SUPER_ADMIN],
  },
  {
    id: 'ports',
    label: 'Port Operations',
    icon: <PortIcon />,
    path: '/ports',
    roles: [UserRole.PORT_OPERATOR, UserRole.ADMIN, UserRole.SUPER_ADMIN],
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: <ReportsIcon />,
    path: '/reports',
  },
  {
    id: 'analytics',
    label: 'Analytics',
    icon: <AnalyticsIcon />,
    path: '/analytics',
    roles: [UserRole.ADMIN, UserRole.SUPER_ADMIN],
  },
  {
    id: 'compliance',
    label: 'Compliance',
    icon: <ComplianceIcon />,
    path: '/compliance',
    roles: [UserRole.CUSTOMS_OFFICER, UserRole.ADMIN, UserRole.SUPER_ADMIN],
  },
  {
    id: 'admin',
    label: 'Administration',
    icon: <AdminIcon />,
    path: '/admin',
    roles: [UserRole.ADMIN, UserRole.SUPER_ADMIN],
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: <SettingsIcon />,
    path: '/settings',
  },
]

const Sidebar: React.FC<SidebarProps> = ({ collapsed, onItemClick }) => {
  const theme = useTheme()
  const location = useLocation()
  const navigate = useNavigate()
  const { user, hasRole } = useAuth()

  const handleItemClick = (path: string) => {
    navigate(path)
    onItemClick?.()
  }

  const isItemActive = (path: string) => {
    return location.pathname.startsWith(path)
  }

  const canAccessItem = (item: NavigationItem) => {
    if (!item.roles) return true
    return item.roles.some(role => hasRole(role))
  }

  const filteredItems = navigationItems.filter(canAccessItem)

  return (
    <Box
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: theme.palette.background.paper,
        borderRight: `1px solid ${theme.palette.divider}`,
      }}
    >
      {/* Logo/Brand */}
      <Box
        sx={{
          p: collapsed ? 1 : 2,
          display: 'flex',
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'flex-start',
          minHeight: 64,
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}
      >
        {collapsed ? (
          <Typography
            variant="h6"
            sx={{
              fontWeight: 700,
              color: theme.palette.primary.main,
              fontSize: '1.2rem',
            }}
          >
            EX
          </Typography>
        ) : (
          <Box>
            <Typography
              variant="h6"
              sx={{
                fontWeight: 700,
                color: theme.palette.primary.main,
                lineHeight: 1,
              }}
            >
              EXIM
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: theme.palette.text.secondary,
                fontSize: '0.7rem',
                lineHeight: 1,
              }}
            >
              Clearance System
            </Typography>
          </Box>
        )}
      </Box>

      {/* User info (when not collapsed) */}
      {!collapsed && user && (
        <Box
          sx={{
            p: 2,
            borderBottom: `1px solid ${theme.palette.divider}`,
            backgroundColor: alpha(theme.palette.primary.main, 0.05),
          }}
        >
          <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
            {user.firstName} {user.lastName}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {user.role.replace('_', ' ').toUpperCase()}
          </Typography>
        </Box>
      )}

      {/* Navigation */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <List sx={{ p: 1 }}>
          {filteredItems.map((item) => {
            const isActive = isItemActive(item.path)
            
            const listItem = (
              <ListItem key={item.id} disablePadding sx={{ mb: 0.5 }}>
                <ListItemButton
                  onClick={() => handleItemClick(item.path)}
                  sx={{
                    borderRadius: 2,
                    minHeight: 48,
                    justifyContent: collapsed ? 'center' : 'flex-start',
                    px: collapsed ? 1.5 : 2,
                    backgroundColor: isActive 
                      ? alpha(theme.palette.primary.main, 0.1)
                      : 'transparent',
                    color: isActive 
                      ? theme.palette.primary.main
                      : theme.palette.text.primary,
                    '&:hover': {
                      backgroundColor: isActive
                        ? alpha(theme.palette.primary.main, 0.15)
                        : alpha(theme.palette.action.hover, 0.08),
                    },
                    '&:before': isActive ? {
                      content: '""',
                      position: 'absolute',
                      left: 0,
                      top: '50%',
                      transform: 'translateY(-50%)',
                      width: 3,
                      height: 24,
                      backgroundColor: theme.palette.primary.main,
                      borderRadius: '0 2px 2px 0',
                    } : {},
                  }}
                >
                  <ListItemIcon
                    sx={{
                      minWidth: collapsed ? 0 : 40,
                      mr: collapsed ? 0 : 1,
                      color: 'inherit',
                      justifyContent: 'center',
                    }}
                  >
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {item.icon}
                    </motion.div>
                  </ListItemIcon>
                  
                  {!collapsed && (
                    <ListItemText
                      primary={item.label}
                      primaryTypographyProps={{
                        fontSize: '0.875rem',
                        fontWeight: isActive ? 600 : 400,
                      }}
                    />
                  )}
                  
                  {!collapsed && item.badge && (
                    <Box
                      sx={{
                        backgroundColor: theme.palette.error.main,
                        color: theme.palette.error.contrastText,
                        borderRadius: '50%',
                        minWidth: 20,
                        height: 20,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '0.75rem',
                        fontWeight: 600,
                      }}
                    >
                      {item.badge}
                    </Box>
                  )}
                </ListItemButton>
              </ListItem>
            )

            return collapsed ? (
              <Tooltip
                key={item.id}
                title={item.label}
                placement="right"
                arrow
              >
                {listItem}
              </Tooltip>
            ) : (
              listItem
            )
          })}
        </List>
      </Box>

      {/* Footer */}
      {!collapsed && (
        <Box
          sx={{
            p: 2,
            borderTop: `1px solid ${theme.palette.divider}`,
            backgroundColor: alpha(theme.palette.background.default, 0.5),
          }}
        >
          <Typography variant="caption" color="text.secondary" align="center">
            © 2024 EXIM System
          </Typography>
        </Box>
      )}
    </Box>
  )
}

export default Sidebar
