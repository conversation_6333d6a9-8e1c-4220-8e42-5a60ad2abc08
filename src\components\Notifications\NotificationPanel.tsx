import React from 'react'
import {
  Menu,
  MenuProps,
  Box,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  IconButton,
  Button,
  Divider,
  Badge,
  Chip,
} from '@mui/material'
import {
  Close as CloseIcon,
  MarkEmailRead as MarkReadIcon,
  Delete as DeleteIcon,
  Info as InfoIcon,
  CheckCircle as SuccessIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
} from '@mui/icons-material'
import { formatDistanceToNow } from 'date-fns'

import { useNotifications } from '@/contexts/NotificationContext'
import { NotificationType } from '@/types'

interface NotificationPanelProps extends Omit<MenuProps, 'children'> {
  onClose: () => void
}

const NotificationPanel: React.FC<NotificationPanelProps> = ({ onClose, ...props }) => {
  const { notifications, markAsRead, markAllAsRead, deleteNotification } = useNotifications()

  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case NotificationType.SUCCESS:
        return <SuccessIcon color="success" />
      case NotificationType.WARNING:
        return <WarningIcon color="warning" />
      case NotificationType.ERROR:
        return <ErrorIcon color="error" />
      default:
        return <InfoIcon color="info" />
    }
  }

  const getNotificationColor = (type: NotificationType) => {
    switch (type) {
      case NotificationType.SUCCESS:
        return 'success'
      case NotificationType.WARNING:
        return 'warning'
      case NotificationType.ERROR:
        return 'error'
      default:
        return 'info'
    }
  }

  const handleMarkAsRead = async (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    await markAsRead(notificationId)
  }

  const handleDelete = async (notificationId: string, event: React.MouseEvent) => {
    event.stopPropagation()
    await deleteNotification(notificationId)
  }

  const handleMarkAllAsRead = async () => {
    await markAllAsRead()
  }

  return (
    <Menu
      {...props}
      onClose={onClose}
      PaperProps={{
        sx: {
          width: 400,
          maxHeight: 600,
          '& .MuiList-root': {
            p: 0,
          },
        },
      }}
      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
    >
      {/* Header */}
      <Box
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Typography variant="h6" sx={{ fontWeight: 600 }}>
          Notifications
        </Typography>
        <Box>
          {notifications.some(n => !n.isRead) && (
            <Button
              size="small"
              onClick={handleMarkAllAsRead}
              sx={{ mr: 1 }}
            >
              Mark all read
            </Button>
          )}
          <IconButton size="small" onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Notifications List */}
      <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
        {notifications.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              No notifications yet
            </Typography>
          </Box>
        ) : (
          <List sx={{ p: 0 }}>
            {notifications.map((notification, index) => (
              <React.Fragment key={notification.id}>
                <ListItem
                  sx={{
                    alignItems: 'flex-start',
                    backgroundColor: notification.isRead ? 'transparent' : 'action.hover',
                    '&:hover': {
                      backgroundColor: 'action.selected',
                    },
                    cursor: notification.actionUrl ? 'pointer' : 'default',
                  }}
                  onClick={() => {
                    if (notification.actionUrl) {
                      window.location.href = notification.actionUrl
                    }
                  }}
                >
                  <ListItemAvatar>
                    <Avatar
                      sx={{
                        bgcolor: `${getNotificationColor(notification.type)}.light`,
                        color: `${getNotificationColor(notification.type)}.main`,
                      }}
                    >
                      {getNotificationIcon(notification.type)}
                    </Avatar>
                  </ListItemAvatar>
                  
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                        <Typography
                          variant="subtitle2"
                          sx={{
                            fontWeight: notification.isRead ? 400 : 600,
                            flexGrow: 1,
                          }}
                        >
                          {notification.title}
                        </Typography>
                        {!notification.isRead && (
                          <Badge
                            color="primary"
                            variant="dot"
                            sx={{ ml: 1 }}
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography
                          variant="body2"
                          color="text.secondary"
                          sx={{ mb: 1 }}
                        >
                          {notification.message}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <Typography variant="caption" color="text.secondary">
                            {formatDistanceToNow(notification.createdAt, { addSuffix: true })}
                          </Typography>
                          <Box>
                            <Chip
                              label={notification.priority}
                              size="small"
                              variant="outlined"
                              color={getNotificationColor(notification.type) as any}
                              sx={{ mr: 1 }}
                            />
                            {!notification.isRead && (
                              <IconButton
                                size="small"
                                onClick={(e) => handleMarkAsRead(notification.id, e)}
                                sx={{ mr: 0.5 }}
                              >
                                <MarkReadIcon fontSize="small" />
                              </IconButton>
                            )}
                            <IconButton
                              size="small"
                              onClick={(e) => handleDelete(notification.id, e)}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Box>
                        </Box>
                      </Box>
                    }
                  />
                </ListItem>
                {index < notifications.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        )}
      </Box>

      {/* Footer */}
      {notifications.length > 0 && (
        <Box
          sx={{
            p: 2,
            borderTop: 1,
            borderColor: 'divider',
            textAlign: 'center',
          }}
        >
          <Button
            variant="text"
            size="small"
            onClick={() => {
              window.location.href = '/notifications'
              onClose()
            }}
          >
            View all notifications
          </Button>
        </Box>
      )}
    </Menu>
  )
}

export default NotificationPanel
