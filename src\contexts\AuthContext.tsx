import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react'
import { User, AuthState, UserRole } from '@/types'
import { authService } from '@/services/authService'
import { toast } from 'react-toastify'

// Auth Context Type
interface AuthContextType extends AuthState {
  login: (email: string, password: string, mfaCode?: string) => Promise<void>
  logout: () => void
  register: (userData: RegisterData) => Promise<void>
  updateProfile: (userData: Partial<User>) => Promise<void>
  refreshToken: () => Promise<void>
  hasPermission: (resource: string, action: string) => boolean
  hasRole: (role: UserRole) => boolean
}

interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  role: UserRole
  organization?: string
  phone?: string
}

// Auth Actions
type AuthAction =
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: { user: User; token: string } }
  | { type: 'LOGIN_FAILURE' }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: User }
  | { type: 'SET_LOADING'; payload: boolean }

// Initial State
const initialState: AuthState = {
  user: null,
  token: localStorage.getItem('auth_token'),
  isAuthenticated: false,
  isLoading: true,
}

// Auth Reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_START':
      return {
        ...state,
        isLoading: true,
      }
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
      }
    case 'LOGIN_FAILURE':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
      }
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
      }
    case 'UPDATE_USER':
      return {
        ...state,
        user: action.payload,
      }
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      }
    default:
      return state
  }
}

// Create Context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Auth Provider Component
interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState)

  // Initialize auth state on app load
  useEffect(() => {
    const initializeAuth = async () => {
      const token = localStorage.getItem('auth_token')
      if (token) {
        try {
          const user = await authService.getCurrentUser()
          dispatch({
            type: 'LOGIN_SUCCESS',
            payload: { user, token },
          })
        } catch (error) {
          localStorage.removeItem('auth_token')
          dispatch({ type: 'LOGIN_FAILURE' })
        }
      } else {
        dispatch({ type: 'SET_LOADING', payload: false })
      }
    }

    initializeAuth()
  }, [])

  // Login function
  const login = async (email: string, password: string, mfaCode?: string) => {
    try {
      dispatch({ type: 'LOGIN_START' })
      
      const response = await authService.login(email, password, mfaCode)
      
      localStorage.setItem('auth_token', response.token)
      
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: {
          user: response.user,
          token: response.token,
        },
      })
      
      toast.success(`Welcome back, ${response.user.firstName}!`)
    } catch (error: any) {
      dispatch({ type: 'LOGIN_FAILURE' })
      toast.error(error.message || 'Login failed')
      throw error
    }
  }

  // Logout function
  const logout = () => {
    localStorage.removeItem('auth_token')
    dispatch({ type: 'LOGOUT' })
    toast.info('You have been logged out')
  }

  // Register function
  const register = async (userData: RegisterData) => {
    try {
      await authService.register(userData)
      toast.success('Registration successful! Please check your email to verify your account.')
    } catch (error: any) {
      toast.error(error.message || 'Registration failed')
      throw error
    }
  }

  // Update profile function
  const updateProfile = async (userData: Partial<User>) => {
    try {
      const updatedUser = await authService.updateProfile(userData)
      dispatch({ type: 'UPDATE_USER', payload: updatedUser })
      toast.success('Profile updated successfully')
    } catch (error: any) {
      toast.error(error.message || 'Profile update failed')
      throw error
    }
  }

  // Refresh token function
  const refreshToken = async () => {
    try {
      const response = await authService.refreshToken()
      localStorage.setItem('auth_token', response.token)
      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: {
          user: response.user,
          token: response.token,
        },
      })
    } catch (error) {
      logout()
      throw error
    }
  }

  // Check if user has specific permission
  const hasPermission = (resource: string, action: string): boolean => {
    if (!state.user) return false
    
    return state.user.permissions.some(
      (permission) =>
        permission.resource === resource && permission.action === action
    )
  }

  // Check if user has specific role
  const hasRole = (role: UserRole): boolean => {
    return state.user?.role === role
  }

  const contextValue: AuthContextType = {
    ...state,
    login,
    logout,
    register,
    updateProfile,
    refreshToken,
    hasPermission,
    hasRole,
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
