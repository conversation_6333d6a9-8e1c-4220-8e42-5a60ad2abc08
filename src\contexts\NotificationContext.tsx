import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react'
import { Notification, NotificationType, Priority } from '@/types'
import { notificationService } from '@/services/notificationService'
import { useAuth } from './AuthContext'

// Notification Context Type
interface NotificationContextType {
  notifications: Notification[]
  unreadCount: number
  isLoading: boolean
  markAsRead: (notificationId: string) => Promise<void>
  markAllAsRead: () => Promise<void>
  deleteNotification: (notificationId: string) => Promise<void>
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void
  fetchNotifications: () => Promise<void>
}

// Notification Actions
type NotificationAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_NOTIFICATIONS'; payload: Notification[] }
  | { type: 'ADD_NOTIFICATION'; payload: Notification }
  | { type: 'UPDATE_NOTIFICATION'; payload: Notification }
  | { type: 'DELETE_NOTIFICATION'; payload: string }
  | { type: 'MARK_AS_READ'; payload: string }
  | { type: 'MARK_ALL_AS_READ' }

// Initial State
interface NotificationState {
  notifications: Notification[]
  isLoading: boolean
}

const initialState: NotificationState = {
  notifications: [],
  isLoading: false,
}

// Notification Reducer
const notificationReducer = (
  state: NotificationState,
  action: NotificationAction
): NotificationState => {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      }
    case 'SET_NOTIFICATIONS':
      return {
        ...state,
        notifications: action.payload,
        isLoading: false,
      }
    case 'ADD_NOTIFICATION':
      return {
        ...state,
        notifications: [action.payload, ...state.notifications],
      }
    case 'UPDATE_NOTIFICATION':
      return {
        ...state,
        notifications: state.notifications.map((notification) =>
          notification.id === action.payload.id ? action.payload : notification
        ),
      }
    case 'DELETE_NOTIFICATION':
      return {
        ...state,
        notifications: state.notifications.filter(
          (notification) => notification.id !== action.payload
        ),
      }
    case 'MARK_AS_READ':
      return {
        ...state,
        notifications: state.notifications.map((notification) =>
          notification.id === action.payload
            ? { ...notification, isRead: true }
            : notification
        ),
      }
    case 'MARK_ALL_AS_READ':
      return {
        ...state,
        notifications: state.notifications.map((notification) => ({
          ...notification,
          isRead: true,
        })),
      }
    default:
      return state
  }
}

// Create Context
const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

// Notification Provider Component
interface NotificationProviderProps {
  children: ReactNode
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(notificationReducer, initialState)
  const { user, isAuthenticated } = useAuth()

  // Calculate unread count
  const unreadCount = state.notifications.filter((notification) => !notification.isRead).length

  // Fetch notifications when user is authenticated
  useEffect(() => {
    if (isAuthenticated && user) {
      fetchNotifications()
    }
  }, [isAuthenticated, user])

  // Set up real-time notifications (WebSocket or Server-Sent Events)
  useEffect(() => {
    if (isAuthenticated && user) {
      const eventSource = notificationService.subscribeToNotifications(user.id)
      
      eventSource.onmessage = (event) => {
        const notification: Notification = JSON.parse(event.data)
        dispatch({ type: 'ADD_NOTIFICATION', payload: notification })
      }

      return () => {
        eventSource.close()
      }
    }
  }, [isAuthenticated, user])

  // Fetch notifications function
  const fetchNotifications = async () => {
    if (!user) return

    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      const notifications = await notificationService.getNotifications(user.id)
      dispatch({ type: 'SET_NOTIFICATIONS', payload: notifications })
    } catch (error) {
      console.error('Failed to fetch notifications:', error)
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }

  // Mark notification as read
  const markAsRead = async (notificationId: string) => {
    try {
      await notificationService.markAsRead(notificationId)
      dispatch({ type: 'MARK_AS_READ', payload: notificationId })
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
    }
  }

  // Mark all notifications as read
  const markAllAsRead = async () => {
    if (!user) return

    try {
      await notificationService.markAllAsRead(user.id)
      dispatch({ type: 'MARK_ALL_AS_READ' })
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error)
    }
  }

  // Delete notification
  const deleteNotification = async (notificationId: string) => {
    try {
      await notificationService.deleteNotification(notificationId)
      dispatch({ type: 'DELETE_NOTIFICATION', payload: notificationId })
    } catch (error) {
      console.error('Failed to delete notification:', error)
    }
  }

  // Add local notification (for immediate feedback)
  const addNotification = (notification: Omit<Notification, 'id' | 'createdAt'>) => {
    const newNotification: Notification = {
      ...notification,
      id: `local_${Date.now()}`,
      createdAt: new Date(),
    }
    dispatch({ type: 'ADD_NOTIFICATION', payload: newNotification })
  }

  const contextValue: NotificationContextType = {
    notifications: state.notifications,
    unreadCount,
    isLoading: state.isLoading,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    addNotification,
    fetchNotifications,
  }

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  )
}

// Custom hook to use notification context
export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext)
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider')
  }
  return context
}

// Helper function to create notifications with different types
export const createNotification = {
  success: (title: string, message: string, actionUrl?: string): Omit<Notification, 'id' | 'createdAt'> => ({
    userId: '',
    title,
    message,
    type: NotificationType.SUCCESS,
    priority: Priority.NORMAL,
    isRead: false,
    actionUrl,
  }),
  
  error: (title: string, message: string, actionUrl?: string): Omit<Notification, 'id' | 'createdAt'> => ({
    userId: '',
    title,
    message,
    type: NotificationType.ERROR,
    priority: Priority.HIGH,
    isRead: false,
    actionUrl,
  }),
  
  warning: (title: string, message: string, actionUrl?: string): Omit<Notification, 'id' | 'createdAt'> => ({
    userId: '',
    title,
    message,
    type: NotificationType.WARNING,
    priority: Priority.NORMAL,
    isRead: false,
    actionUrl,
  }),
  
  info: (title: string, message: string, actionUrl?: string): Omit<Notification, 'id' | 'createdAt'> => ({
    userId: '',
    title,
    message,
    type: NotificationType.INFO,
    priority: Priority.LOW,
    isRead: false,
    actionUrl,
  }),
}
