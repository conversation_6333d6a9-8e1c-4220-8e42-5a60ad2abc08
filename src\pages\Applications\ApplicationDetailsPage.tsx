import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Chip,
  Button,
  Divider,

  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
} from '@mui/material'
import {
  ArrowBack as BackIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreIcon,
  Download as DownloadIcon,
  Print as PrintIcon,
  Share as ShareIcon,
  CheckCircle as CompletedIcon,
  Schedule as PendingIcon,
  Warning as WarningIcon,
  Payment as PaymentIcon,
  LocalShipping as ShippingIcon,
} from '@mui/icons-material'
import {
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
} from '@mui/lab'
import { motion } from 'framer-motion'
import { toast } from 'react-toastify'

import { Application, ApplicationStatus } from '@/types'
import { useApplications } from '@/contexts/ApplicationContext'
import StatusChip from '@/components/Common/StatusChip'
import LoadingSpinner from '@/components/Common/LoadingSpinner'

const ApplicationDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { applications, updateApplication, deleteApplication, loading } = useApplications()
  
  const [application, setApplication] = useState<Application | null>(null)
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [statusDialogOpen, setStatusDialogOpen] = useState(false)
  const [newStatus, setNewStatus] = useState<ApplicationStatus | ''>('')
  const [statusNote, setStatusNote] = useState('')

  useEffect(() => {
    if (id) {
      const foundApplication = applications.find(app => app.id === id)
      setApplication(foundApplication || null)
    }
  }, [id, applications])

  if (loading) {
    return <LoadingSpinner message="Loading application details..." />
  }

  if (!application) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="error">
          Application not found
        </Typography>
        <Button
          variant="outlined"
          onClick={() => navigate('/applications')}
          sx={{ mt: 2 }}
        >
          Back to Applications
        </Button>
      </Box>
    )
  }

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
  }

  const handleDelete = async () => {
    try {
      await deleteApplication(application.id)
      navigate('/applications')
    } catch (error) {
      toast.error('Failed to delete application')
    }
    setDeleteDialogOpen(false)
  }

  const handleStatusUpdate = async () => {
    if (!newStatus) return

    try {
      await updateApplication(application.id, {
        status: newStatus,
        updatedAt: new Date(),
      })
      setApplication(prev => prev ? { ...prev, status: newStatus, updatedAt: new Date() } : null)
      toast.success('Status updated successfully')
    } catch (error) {
      toast.error('Failed to update status')
    }
    setStatusDialogOpen(false)
    setNewStatus('')
    setStatusNote('')
  }

  const getStatusIcon = (status: ApplicationStatus) => {
    switch (status) {
      case ApplicationStatus.COMPLETED:
        return <CompletedIcon color="success" />
      case ApplicationStatus.UNDER_REVIEW:
        return <PendingIcon color="warning" />
      case ApplicationStatus.PENDING_PAYMENT:
        return <PaymentIcon color="info" />
      default:
        return <WarningIcon color="action" />
    }
  }

  const mockTrackingEvents = [
    {
      id: '1',
      title: 'Application Submitted',
      description: 'Application submitted by applicant',
      timestamp: application.submissionDate,
      status: 'completed',
    },
    {
      id: '2',
      title: 'Document Verification',
      description: 'Documents are being verified by customs',
      timestamp: new Date(application.submissionDate.getTime() + 24 * 60 * 60 * 1000),
      status: application.status === ApplicationStatus.COMPLETED ? 'completed' : 'current',
    },
    {
      id: '3',
      title: 'Payment Processing',
      description: 'Processing payment and fees',
      timestamp: new Date(application.submissionDate.getTime() + 48 * 60 * 60 * 1000),
      status: application.status === ApplicationStatus.COMPLETED ? 'completed' : 'pending',
    },
    {
      id: '4',
      title: 'Final Approval',
      description: 'Final approval and certificate generation',
      timestamp: application.completionDate || new Date(application.submissionDate.getTime() + 72 * 60 * 60 * 1000),
      status: application.status === ApplicationStatus.COMPLETED ? 'completed' : 'pending',
    },
  ]

  return (
    <Box sx={{ p: 3 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <IconButton onClick={() => navigate('/applications')} sx={{ mr: 2 }}>
            <BackIcon />
          </IconButton>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h4" sx={{ fontWeight: 600 }}>
              {application.applicationNumber}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {application.type} Application • Submitted {application.submissionDate.toLocaleDateString()}
            </Typography>
          </Box>
          <StatusChip status={application.status} />
          <IconButton onClick={handleMenuOpen} sx={{ ml: 1 }}>
            <MoreIcon />
          </IconButton>
        </Box>

        <Grid container spacing={3}>
          {/* Application Details */}
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Application Information
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Application Type</Typography>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {application.type}
                    </Typography>
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Priority</Typography>
                    <StatusChip status={application.priority} />
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Submission Date</Typography>
                    <Typography variant="body1">
                      {application.submissionDate.toLocaleDateString()}
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Expected Completion</Typography>
                    <Typography variant="body1">
                      {application.expectedCompletionDate?.toLocaleDateString()}
                    </Typography>
                  </Box>
                  {application.completionDate && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">Actual Completion</Typography>
                      <Typography variant="body1">
                        {application.completionDate.toLocaleDateString()}
                      </Typography>
                    </Box>
                  )}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Last Updated</Typography>
                    <Typography variant="body1">
                      {application.updatedAt.toLocaleDateString()}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Paper>

            {/* Applicant Information */}
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Applicant Information
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Company</Typography>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {application.applicant.organization}
                    </Typography>
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Contact Person</Typography>
                    <Typography variant="body1">
                      {application.applicant.firstName} {application.applicant.lastName}
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Email</Typography>
                    <Typography variant="body1">
                      {application.applicant.email}
                    </Typography>
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Role</Typography>
                    <Typography variant="body1">
                      {application.applicant.role.replace('_', ' ').toUpperCase()}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Paper>

            {/* Cargo Information */}
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Cargo Information
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">Description</Typography>
                <Typography variant="body1">
                  {application.cargo.description}
                </Typography>
              </Box>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">HS Code</Typography>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {application.cargo.hsCode}
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Quantity</Typography>
                    <Typography variant="body1">
                      {application.cargo.quantity} {application.cargo.unit}
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Weight</Typography>
                    <Typography variant="body1">
                      {application.cargo.weight} kg
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Value</Typography>
                    <Typography variant="body1" sx={{ fontWeight: 500 }}>
                      {application.cargo.currency} {application.cargo.value.toLocaleString()}
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Origin</Typography>
                    <Typography variant="body1">
                      {application.cargo.origin}
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Destination</Typography>
                    <Typography variant="body1">
                      {application.cargo.destination}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Paper>
          </Grid>

          {/* Sidebar */}
          <Grid item xs={12} md={4}>
            {/* Quick Actions */}
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Quick Actions
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button
                  variant="outlined"
                  startIcon={<EditIcon />}
                  onClick={() => setStatusDialogOpen(true)}
                  fullWidth
                >
                  Update Status
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<DownloadIcon />}
                  fullWidth
                >
                  Download Certificate
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<PrintIcon />}
                  fullWidth
                >
                  Print Application
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<ShareIcon />}
                  fullWidth
                >
                  Share Application
                </Button>
              </Box>
            </Paper>

            {/* Progress Timeline */}
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                Progress Timeline
              </Typography>
              
              <Timeline>
                {mockTrackingEvents.map((event, index) => (
                  <TimelineItem key={event.id}>
                    <TimelineSeparator>
                      <TimelineDot
                        color={
                          event.status === 'completed'
                            ? 'success'
                            : event.status === 'current'
                            ? 'primary'
                            : 'grey'
                        }
                      >
                        {getStatusIcon(application.status)}
                      </TimelineDot>
                      {index < mockTrackingEvents.length - 1 && <TimelineConnector />}
                    </TimelineSeparator>
                    <TimelineContent>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                        {event.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {event.description}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {event.timestamp.toLocaleDateString()}
                      </Typography>
                    </TimelineContent>
                  </TimelineItem>
                ))}
              </Timeline>
            </Paper>
          </Grid>
        </Grid>

        {/* Action Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => { handleMenuClose(); setStatusDialogOpen(true); }}>
            <EditIcon sx={{ mr: 1 }} />
            Update Status
          </MenuItem>
          <MenuItem onClick={() => { handleMenuClose(); /* Handle edit */ }}>
            <EditIcon sx={{ mr: 1 }} />
            Edit Application
          </MenuItem>
          <MenuItem onClick={() => { handleMenuClose(); /* Handle download */ }}>
            <DownloadIcon sx={{ mr: 1 }} />
            Download
          </MenuItem>
          <Divider />
          <MenuItem 
            onClick={() => { handleMenuClose(); setDeleteDialogOpen(true); }}
            sx={{ color: 'error.main' }}
          >
            <DeleteIcon sx={{ mr: 1 }} />
            Delete
          </MenuItem>
        </Menu>

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
          <DialogTitle>Delete Application</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete this application? This action cannot be undone.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleDelete} color="error" variant="contained">
              Delete
            </Button>
          </DialogActions>
        </Dialog>

        {/* Status Update Dialog */}
        <Dialog open={statusDialogOpen} onClose={() => setStatusDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Update Application Status</DialogTitle>
          <DialogContent>
            <Box sx={{ mt: 2 }}>
              <TextField
                select
                fullWidth
                label="New Status"
                value={newStatus}
                onChange={(e) => setNewStatus(e.target.value as ApplicationStatus)}
                sx={{ mb: 3 }}
              >
                {Object.values(ApplicationStatus).map((status) => (
                  <MenuItem key={status} value={status}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <StatusChip status={status} />
                      {status.replace('_', ' ').toUpperCase()}
                    </Box>
                  </MenuItem>
                ))}
              </TextField>
              
              <TextField
                fullWidth
                label="Status Note (Optional)"
                multiline
                rows={3}
                value={statusNote}
                onChange={(e) => setStatusNote(e.target.value)}
                placeholder="Add a note about this status change..."
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setStatusDialogOpen(false)}>Cancel</Button>
            <Button 
              onClick={handleStatusUpdate} 
              variant="contained"
              disabled={!newStatus}
            >
              Update Status
            </Button>
          </DialogActions>
        </Dialog>
      </motion.div>
    </Box>
  )
}

export default ApplicationDetailsPage
