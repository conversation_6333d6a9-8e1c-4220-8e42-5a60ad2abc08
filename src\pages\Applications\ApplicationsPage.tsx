import React, { useState, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Box,
  Typography,
  Button,
  Tabs,
  Tab,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Paper,
  Avatar,
  LinearProgress,
  Fab,
  Alert,
} from '@mui/material'
import {
  Add as AddIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  MoreVert as MoreIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  GetApp as ExportIcon,
  TrendingUp as TrendingUpIcon,
  Schedule as PendingIcon,
  CheckCircle as CompletedIcon,
  Warning as WarningIcon,
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'
import { toast } from 'react-toastify'

import DataTable, { Column } from '@/components/Common/DataTable'
import SearchFilter from '@/components/Common/SearchFilter'
import StatusChip from '@/components/Common/StatusChip'
import LoadingSpinner from '@/components/Common/LoadingSpinner'
import { Application, ApplicationStatus, ApplicationType, Priority } from '@/types'
import { useApplications } from '@/contexts/ApplicationContext'

const ApplicationsPage: React.FC = () => {
  const navigate = useNavigate()
  const {
    applications,
    loading,
    error,
    selectedApplication,
    filters,
    searchTerm,
    setSelectedApplication,
    setFilters,
    setSearchTerm,
    deleteApplication,
    refreshApplications,
  } = useApplications()

  const [currentTab, setCurrentTab] = useState(0)
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const [menuApplication, setMenuApplication] = useState<Application | null>(null)
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue)
  }

  const handleNewApplication = () => {
    navigate('/applications/new')
  }

  const handleViewApplication = (application: Application) => {
    navigate(`/applications/${application.id}`)
  }

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, application: Application) => {
    event.stopPropagation()
    setAnchorEl(event.currentTarget)
    setMenuApplication(application)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
    setMenuApplication(null)
  }

  const handleDelete = async () => {
    if (menuApplication) {
      try {
        await deleteApplication(menuApplication.id)
        toast.success('Application deleted successfully')
      } catch (error) {
        toast.error('Failed to delete application')
      }
    }
    setDeleteDialogOpen(false)
    handleMenuClose()
  }

  const handleExport = () => {
    // Mock export functionality
    toast.info('Export functionality coming soon')
  }

  // Filter applications based on current tab
  const filteredApplications = useMemo(() => {
    let filtered = applications

    // Apply tab filter
    switch (currentTab) {
      case 1: // My Applications
        filtered = filtered.filter(app => app.applicantId === '1') // Mock current user ID
        break
      case 2: // Pending Review
        filtered = filtered.filter(app =>
          app.status === ApplicationStatus.SUBMITTED ||
          app.status === ApplicationStatus.UNDER_REVIEW
        )
        break
      case 3: // Completed
        filtered = filtered.filter(app => app.status === ApplicationStatus.COMPLETED)
        break
      default: // All Applications
        break
    }

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(app =>
        app.applicationNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.applicant.organization?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.cargo.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Apply status filters
    if (filters.status && filters.status.length > 0) {
      filtered = filtered.filter(app => filters.status.includes(app.status))
    }

    // Apply type filters
    if (filters.type && filters.type.length > 0) {
      filtered = filtered.filter(app => filters.type.includes(app.type))
    }

    // Apply priority filters
    if (filters.priority && filters.priority.length > 0) {
      filtered = filtered.filter(app => filters.priority.includes(app.priority))
    }

    return filtered
  }, [applications, currentTab, searchTerm, filters])

  // Calculate statistics
  const stats = useMemo(() => {
    const total = applications.length
    const pending = applications.filter(app =>
      app.status === ApplicationStatus.SUBMITTED ||
      app.status === ApplicationStatus.UNDER_REVIEW
    ).length
    const completed = applications.filter(app =>
      app.status === ApplicationStatus.COMPLETED
    ).length
    const urgent = applications.filter(app =>
      app.priority === Priority.URGENT
    ).length

    return { total, pending, completed, urgent }
  }, [applications])

  const columns: Column[] = [
    {
      id: 'applicationNumber',
      label: 'Application #',
      minWidth: 150,
      format: (value, row) => (
        <Box sx={{ cursor: 'pointer' }} onClick={() => handleViewApplication(row)}>
          <Typography variant="body2" sx={{ fontWeight: 600, color: 'primary.main' }}>
            {value}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {row.applicant.organization}
          </Typography>
        </Box>
      ),
    },
    {
      id: 'type',
      label: 'Type',
      minWidth: 100,
      format: (value) => (
        <Chip
          label={value.replace('_', ' ')}
          size="small"
          variant="outlined"
          color="primary"
        />
      ),
    },
    {
      id: 'status',
      label: 'Status',
      minWidth: 120,
      format: (value) => <StatusChip status={value} />,
    },
    {
      id: 'priority',
      label: 'Priority',
      minWidth: 100,
      format: (value) => <StatusChip status={value} />,
    },
    {
      id: 'submissionDate',
      label: 'Submitted',
      minWidth: 120,
      format: (value) => new Date(value).toLocaleDateString(),
    },
    {
      id: 'cargo',
      label: 'Cargo Value',
      minWidth: 120,
      format: (value) => `${value.currency} ${value.value.toLocaleString()}`,
    },
    {
      id: 'actions',
      label: 'Actions',
      minWidth: 100,
      sortable: false,
      format: (_, row) => (
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton size="small" onClick={() => handleViewApplication(row)}>
            <ViewIcon />
          </IconButton>
          <IconButton
            size="small"
            onClick={(e) => handleMenuOpen(e, row)}
          >
            <MoreIcon />
          </IconButton>
        </Box>
      ),
    },
  ]

  const filterConfigs = [
    {
      id: 'status',
      label: 'Status',
      type: 'multiselect' as const,
      options: Object.values(ApplicationStatus).map(status => ({
        label: status.replace('_', ' ').toUpperCase(),
        value: status,
      })),
    },
    {
      id: 'type',
      label: 'Type',
      type: 'multiselect' as const,
      options: Object.values(ApplicationType).map(type => ({
        label: type.replace('_', ' ').toUpperCase(),
        value: type,
      })),
    },
    {
      id: 'priority',
      label: 'Priority',
      type: 'multiselect' as const,
      options: Object.values(Priority).map(priority => ({
        label: priority.replace('_', ' ').toUpperCase(),
        value: priority,
      })),
    },
    {
      id: 'dateRange',
      label: 'Submission Date',
      type: 'daterange' as const,
    },
  ]

  if (loading) {
    return <LoadingSpinner message="Loading applications..." />
  }

  return (
    <Box sx={{ p: 3 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 600 }}>
              Applications
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Manage and track all import/export applications
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={refreshApplications}
              disabled={loading}
            >
              Refresh
            </Button>
            <Button
              variant="outlined"
              startIcon={<ExportIcon />}
              onClick={handleExport}
            >
              Export
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              size="large"
              onClick={handleNewApplication}
            >
              New Application
            </Button>
          </Box>
        </Box>

        {/* Statistics Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                      <TrendingUpIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h4" sx={{ fontWeight: 700 }}>
                        {stats.total}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Applications
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                      <PendingIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h4" sx={{ fontWeight: 700 }}>
                        {stats.pending}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Pending Review
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                      <CompletedIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h4" sx={{ fontWeight: 700 }}>
                        {stats.completed}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Completed
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar sx={{ bgcolor: 'error.main', mr: 2 }}>
                      <WarningIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h4" sx={{ fontWeight: 700 }}>
                        {stats.urgent}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Urgent Priority
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        </Grid>

        {/* Error Alert */}
        {error && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            transition={{ duration: 0.3 }}
          >
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          </motion.div>
        )}

        {/* Tabs */}
        <Tabs value={currentTab} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label={`All Applications (${applications.length})`} />
          <Tab label={`My Applications (${applications.filter(app => app.applicantId === '1').length})`} />
          <Tab label={`Pending Review (${stats.pending})`} />
          <Tab label={`Completed (${stats.completed})`} />
        </Tabs>

        {/* Search and Filters */}
        <SearchFilter
          searchValue={searchTerm}
          onSearchChange={setSearchTerm}
          filters={filterConfigs}
          activeFilters={filters}
          onFiltersChange={setFilters}
          onClearAll={() => {
            setSearchTerm('')
            setFilters({})
          }}
        />

        {/* Applications Table */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <DataTable
            columns={columns}
            rows={filteredApplications}
            title={`Applications (${filteredApplications.length})`}
            selectable
            searchable={false} // Using custom search
            onRowClick={handleViewApplication}
            emptyMessage="No applications found"
            loading={loading}
            refreshable
            onRefresh={refreshApplications}
            exportable
            onExport={handleExport}
          />
        </motion.div>

        {/* Floating Action Button */}
        <Fab
          color="primary"
          aria-label="add"
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
          }}
          onClick={handleNewApplication}
        >
          <AddIcon />
        </Fab>

        {/* Action Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => { handleMenuClose(); menuApplication && handleViewApplication(menuApplication); }}>
            <ViewIcon sx={{ mr: 1 }} />
            View Details
          </MenuItem>
          <MenuItem onClick={() => { handleMenuClose(); /* Handle edit */ }}>
            <EditIcon sx={{ mr: 1 }} />
            Edit Application
          </MenuItem>
          <MenuItem onClick={() => { handleMenuClose(); /* Handle duplicate */ }}>
            <AddIcon sx={{ mr: 1 }} />
            Duplicate
          </MenuItem>
          <MenuItem onClick={() => { handleMenuClose(); /* Handle download */ }}>
            <ExportIcon sx={{ mr: 1 }} />
            Download
          </MenuItem>
          <MenuItem
            onClick={() => { handleMenuClose(); setDeleteDialogOpen(true); }}
            sx={{ color: 'error.main' }}
          >
            <DeleteIcon sx={{ mr: 1 }} />
            Delete
          </MenuItem>
        </Menu>

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
          <DialogTitle>Delete Application</DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to delete application {menuApplication?.applicationNumber}?
              This action cannot be undone.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleDelete} color="error" variant="contained">
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </motion.div>
    </Box>
  )
}

export default ApplicationsPage
