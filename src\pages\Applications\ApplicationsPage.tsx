import React, { useState } from 'react'
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Tabs,
  Tab,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material'
import {
  Add as AddIcon,
  FilterList as FilterIcon,
  Search as SearchIcon,
  MoreVert as MoreIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'

import DataTable, { Column } from '@/components/Common/DataTable'
import SearchFilter from '@/components/Common/SearchFilter'
import StatusChip from '@/components/Common/StatusChip'
import ApplicationForm from '@/components/Applications/ApplicationForm'
import { Application, ApplicationStatus, ApplicationType } from '@/types'

// Mock data
const mockApplications: Application[] = [
  {
    id: '1',
    applicationNumber: 'APP-2024-001',
    applicantId: '1',
    applicant: { firstName: '<PERSON>', lastName: 'Doe' } as any,
    type: ApplicationType.IMPORT,
    status: ApplicationStatus.UNDER_REVIEW,
    priority: 'high' as any,
    submissionDate: new Date('2024-01-15'),
    expectedCompletionDate: new Date('2024-01-25'),
    documents: [],
    payments: [],
    trackingEvents: [],
    cargo: {
      description: 'Electronic Components',
      value: 50000,
      currency: 'USD',
    } as any,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15'),
  },
  // Add more mock data as needed
]

const ApplicationsPage: React.FC = () => {
  const [currentTab, setCurrentTab] = useState(0)
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState({})
  const [showNewApplicationDialog, setShowNewApplicationDialog] = useState(false)
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null)

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setCurrentTab(newValue)
  }

  const handleNewApplication = () => {
    setShowNewApplicationDialog(true)
  }

  const handleApplicationSubmit = async (data: any) => {
    console.log('Submitting application:', data)
    // Handle application submission
    setShowNewApplicationDialog(false)
  }

  const columns: Column[] = [
    {
      id: 'applicationNumber',
      label: 'Application #',
      minWidth: 150,
      format: (value) => (
        <Typography variant="body2" sx={{ fontWeight: 600 }}>
          {value}
        </Typography>
      ),
    },
    {
      id: 'type',
      label: 'Type',
      minWidth: 100,
      format: (value) => (
        <Chip
          label={value.replace('_', ' ')}
          size="small"
          variant="outlined"
          color="primary"
        />
      ),
    },
    {
      id: 'status',
      label: 'Status',
      minWidth: 120,
      format: (value) => <StatusChip status={value} />,
    },
    {
      id: 'priority',
      label: 'Priority',
      minWidth: 100,
      format: (value) => <StatusChip status={value} />,
    },
    {
      id: 'submissionDate',
      label: 'Submitted',
      minWidth: 120,
      format: (value) => new Date(value).toLocaleDateString(),
    },
    {
      id: 'cargo',
      label: 'Cargo Value',
      minWidth: 120,
      format: (value) => `${value.currency} ${value.value.toLocaleString()}`,
    },
    {
      id: 'actions',
      label: 'Actions',
      minWidth: 100,
      sortable: false,
      format: (_, row) => (
        <Box sx={{ display: 'flex', gap: 1 }}>
          <IconButton size="small" onClick={() => setSelectedApplication(row)}>
            <ViewIcon />
          </IconButton>
          <IconButton size="small">
            <EditIcon />
          </IconButton>
          <IconButton size="small" color="error">
            <DeleteIcon />
          </IconButton>
        </Box>
      ),
    },
  ]

  const filterConfigs = [
    {
      id: 'status',
      label: 'Status',
      type: 'multiselect' as const,
      options: Object.values(ApplicationStatus).map(status => ({
        label: status.replace('_', ' ').toUpperCase(),
        value: status,
      })),
    },
    {
      id: 'type',
      label: 'Type',
      type: 'multiselect' as const,
      options: Object.values(ApplicationType).map(type => ({
        label: type.replace('_', ' ').toUpperCase(),
        value: type,
      })),
    },
    {
      id: 'dateRange',
      label: 'Submission Date',
      type: 'daterange' as const,
    },
  ]

  return (
    <Box sx={{ p: 3 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" sx={{ fontWeight: 600 }}>
            Applications
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            size="large"
            onClick={handleNewApplication}
          >
            New Application
          </Button>
        </Box>

        {/* Tabs */}
        <Tabs value={currentTab} onChange={handleTabChange} sx={{ mb: 3 }}>
          <Tab label="All Applications" />
          <Tab label="My Applications" />
          <Tab label="Pending Review" />
          <Tab label="Completed" />
        </Tabs>

        {/* Search and Filters */}
        <SearchFilter
          searchValue={searchTerm}
          onSearchChange={setSearchTerm}
          filters={filterConfigs}
          activeFilters={filters}
          onFiltersChange={setFilters}
          onClearAll={() => {
            setSearchTerm('')
            setFilters({})
          }}
        />

        {/* Applications Table */}
        <DataTable
          columns={columns}
          rows={mockApplications}
          title="Applications"
          selectable
          searchable={false} // Using custom search
          onRowClick={(row) => setSelectedApplication(row)}
          emptyMessage="No applications found"
        />

        {/* New Application Dialog */}
        <Dialog
          open={showNewApplicationDialog}
          onClose={() => setShowNewApplicationDialog(false)}
          maxWidth="md"
          fullWidth
          PaperProps={{
            sx: { height: '90vh' },
          }}
        >
          <DialogTitle>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              Create New Application
            </Typography>
          </DialogTitle>
          <DialogContent sx={{ p: 0 }}>
            <ApplicationForm
              onSubmit={handleApplicationSubmit}
              onSaveDraft={async (data) => {
                console.log('Saving draft:', data)
              }}
            />
          </DialogContent>
        </Dialog>
      </motion.div>
    </Box>
  )
}

export default ApplicationsPage
