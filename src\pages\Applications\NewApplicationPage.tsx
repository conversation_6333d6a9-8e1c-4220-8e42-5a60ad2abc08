import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Box,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Button,
  Typography,
  Paper,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Card,
  CardContent,
  Alert,
  Divider,
  Chip,
  LinearProgress,
} from '@mui/material'
import {
  Business as BusinessIcon,
  LocalShipping as ShippingIcon,
  AttachFile as DocumentIcon,
  CheckCircle as CompleteIcon,
  Save as SaveIcon,
  Send as SubmitIcon,
} from '@mui/icons-material'
import { motion, AnimatePresence } from 'framer-motion'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { toast } from 'react-toastify'

import { ApplicationType, Priority } from '@/types'
import { useApplications } from '@/contexts/ApplicationContext'
import FileUpload from '@/components/Common/FileUpload'

// Validation schemas
const basicInfoSchema = yup.object({
  type: yup.string().required('Application type is required'),
  priority: yup.string().required('Priority is required'),
  companyName: yup.string().required('Company name is required'),
  contactPerson: yup.string().required('Contact person is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  phone: yup.string().required('Phone number is required'),
  address: yup.string().required('Address is required'),
})

const cargoDetailsSchema = yup.object({
  cargoDescription: yup.string().required('Cargo description is required'),
  hsCode: yup.string().required('HS Code is required'),
  quantity: yup.number().positive('Quantity must be positive').required('Quantity is required'),
  unit: yup.string().required('Unit is required'),
  weight: yup.number().positive('Weight must be positive').required('Weight is required'),
  value: yup.number().positive('Value must be positive').required('Value is required'),
  currency: yup.string().required('Currency is required'),
  origin: yup.string().required('Origin is required'),
  destination: yup.string().required('Destination is required'),
})

const steps = [
  {
    label: 'Basic Information',
    description: 'Application type and company details',
    icon: <BusinessIcon />,
  },
  {
    label: 'Cargo Details',
    description: 'Cargo information and specifications',
    icon: <ShippingIcon />,
  },
  {
    label: 'Documents',
    description: 'Upload required documents',
    icon: <DocumentIcon />,
  },
  {
    label: 'Review & Submit',
    description: 'Review and submit application',
    icon: <CompleteIcon />,
  },
]

const NewApplicationPage: React.FC = () => {
  const navigate = useNavigate()
  const { createApplication, loading } = useApplications()
  
  const [activeStep, setActiveStep] = useState(0)
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([])
  const [formData, setFormData] = useState<any>({})

  const {
    control: basicControl,
    handleSubmit: handleBasicSubmit,
    formState: { errors: basicErrors },
    watch: watchBasic,
    getValues: getBasicValues,
  } = useForm({
    resolver: yupResolver(basicInfoSchema),
    defaultValues: {
      type: '',
      priority: '',
      companyName: '',
      contactPerson: '',
      email: '',
      phone: '',
      address: '',
    },
  })

  const {
    control: cargoControl,
    handleSubmit: handleCargoSubmit,
    formState: { errors: cargoErrors },
    watch: watchCargo,
    getValues: getCargoValues,
  } = useForm({
    resolver: yupResolver(cargoDetailsSchema),
    defaultValues: {
      cargoDescription: '',
      hsCode: '',
      quantity: 0,
      unit: '',
      weight: 0,
      value: 0,
      currency: '',
      origin: '',
      destination: '',
    },
  })

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1)
  }

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1)
  }

  const handleBasicInfoNext = (data: any) => {
    setFormData((prev: any) => ({ ...prev, ...data }))
    handleNext()
  }

  const handleCargoDetailsNext = (data: any) => {
    setFormData((prev: any) => ({ ...prev, ...data }))
    handleNext()
  }

  const handleFilesUpload = async (files: File[]) => {
    setUploadedFiles((prev) => [...prev, ...files])
    toast.success(`${files.length} file(s) uploaded successfully`)
  }

  const handleFinalSubmit = async () => {
    try {
      const basicData = getBasicValues()
      const cargoData = getCargoValues()
      
      const applicationData = {
        ...basicData,
        ...cargoData,
        documents: uploadedFiles,
        type: basicData.type as ApplicationType,
        priority: basicData.priority as Priority,
      }

      await createApplication(applicationData)
      toast.success('Application submitted successfully!')
      navigate('/applications')
    } catch (error) {
      toast.error('Failed to submit application')
    }
  }

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Controller
                  name="type"
                  control={basicControl}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!basicErrors.type}>
                      <InputLabel>Application Type *</InputLabel>
                      <Select {...field} label="Application Type *">
                        <MenuItem value={ApplicationType.IMPORT}>Import</MenuItem>
                        <MenuItem value={ApplicationType.EXPORT}>Export</MenuItem>
                        <MenuItem value={ApplicationType.TRANSIT}>Transit</MenuItem>
                        <MenuItem value={ApplicationType.TEMPORARY_IMPORT}>Temporary Import</MenuItem>
                        <MenuItem value={ApplicationType.RE_EXPORT}>Re-export</MenuItem>
                      </Select>
                      {basicErrors.type && (
                        <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                          {basicErrors.type.message}
                        </Typography>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="priority"
                  control={basicControl}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!basicErrors.priority}>
                      <InputLabel>Priority *</InputLabel>
                      <Select {...field} label="Priority *">
                        <MenuItem value={Priority.LOW}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip label="Low" size="small" color="default" />
                            Low Priority
                          </Box>
                        </MenuItem>
                        <MenuItem value={Priority.NORMAL}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip label="Normal" size="small" color="info" />
                            Normal Priority
                          </Box>
                        </MenuItem>
                        <MenuItem value={Priority.HIGH}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip label="High" size="small" color="warning" />
                            High Priority
                          </Box>
                        </MenuItem>
                        <MenuItem value={Priority.URGENT}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip label="Urgent" size="small" color="error" />
                            Urgent Priority
                          </Box>
                        </MenuItem>
                      </Select>
                      {basicErrors.priority && (
                        <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                          {basicErrors.priority.message}
                        </Typography>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="companyName"
                  control={basicControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Company Name *"
                      error={!!basicErrors.companyName}
                      helperText={basicErrors.companyName?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="contactPerson"
                  control={basicControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Contact Person *"
                      error={!!basicErrors.contactPerson}
                      helperText={basicErrors.contactPerson?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="email"
                  control={basicControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Email Address *"
                      type="email"
                      error={!!basicErrors.email}
                      helperText={basicErrors.email?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="phone"
                  control={basicControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Phone Number *"
                      error={!!basicErrors.phone}
                      helperText={basicErrors.phone?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <Controller
                  name="address"
                  control={basicControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Company Address *"
                      multiline
                      rows={3}
                      error={!!basicErrors.address}
                      helperText={basicErrors.address?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </motion.div>
        )

      case 1:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Controller
                  name="cargoDescription"
                  control={cargoControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Cargo Description *"
                      multiline
                      rows={3}
                      error={!!cargoErrors.cargoDescription}
                      helperText={cargoErrors.cargoDescription?.message || 'Provide detailed description of the cargo'}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="hsCode"
                  control={cargoControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="HS Code *"
                      error={!!cargoErrors.hsCode}
                      helperText={cargoErrors.hsCode?.message || 'Harmonized System Code'}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={3}>
                <Controller
                  name="quantity"
                  control={cargoControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Quantity *"
                      type="number"
                      error={!!cargoErrors.quantity}
                      helperText={cargoErrors.quantity?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={3}>
                <Controller
                  name="unit"
                  control={cargoControl}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!cargoErrors.unit}>
                      <InputLabel>Unit *</InputLabel>
                      <Select {...field} label="Unit *">
                        <MenuItem value="kg">Kilograms</MenuItem>
                        <MenuItem value="tons">Tons</MenuItem>
                        <MenuItem value="pieces">Pieces</MenuItem>
                        <MenuItem value="containers">Containers</MenuItem>
                        <MenuItem value="pallets">Pallets</MenuItem>
                        <MenuItem value="boxes">Boxes</MenuItem>
                        <MenuItem value="liters">Liters</MenuItem>
                      </Select>
                      {cargoErrors.unit && (
                        <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                          {cargoErrors.unit.message}
                        </Typography>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <Controller
                  name="weight"
                  control={cargoControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Weight (kg) *"
                      type="number"
                      error={!!cargoErrors.weight}
                      helperText={cargoErrors.weight?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <Controller
                  name="value"
                  control={cargoControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Value *"
                      type="number"
                      error={!!cargoErrors.value}
                      helperText={cargoErrors.value?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <Controller
                  name="currency"
                  control={cargoControl}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!cargoErrors.currency}>
                      <InputLabel>Currency *</InputLabel>
                      <Select {...field} label="Currency *">
                        <MenuItem value="NGN">Nigerian Naira (NGN)</MenuItem>
                        <MenuItem value="USD">US Dollar (USD)</MenuItem>
                        <MenuItem value="EUR">Euro (EUR)</MenuItem>
                        <MenuItem value="GBP">British Pound (GBP)</MenuItem>
                        <MenuItem value="CNY">Chinese Yuan (CNY)</MenuItem>
                      </Select>
                      {cargoErrors.currency && (
                        <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                          {cargoErrors.currency.message}
                        </Typography>
                      )}
                    </FormControl>
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="origin"
                  control={cargoControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Origin Country *"
                      error={!!cargoErrors.origin}
                      helperText={cargoErrors.origin?.message}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <Controller
                  name="destination"
                  control={cargoControl}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Destination Country *"
                      error={!!cargoErrors.destination}
                      helperText={cargoErrors.destination?.message}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </motion.div>
        )

      case 2:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Box>
              <Typography variant="h6" gutterBottom>
                Upload Required Documents
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Please upload all required documents for your application. Accepted formats: PDF, DOC, DOCX, JPG, PNG
              </Typography>
              
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  <strong>Required documents may include:</strong>
                  <br />
                  • Commercial Invoice
                  <br />
                  • Bill of Lading / Airway Bill
                  <br />
                  • Packing List
                  <br />
                  • Certificate of Origin
                  <br />
                  • Import/Export License
                  <br />
                  • Insurance Certificate
                </Typography>
              </Alert>
              
              <FileUpload
                onFilesUpload={handleFilesUpload}
                acceptedFileTypes={['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png']}
                maxFileSize={10 * 1024 * 1024} // 10MB
                maxFiles={10}
                helperText="Drag and drop files here or click to browse"
              />

              {uploadedFiles.length > 0 && (
                <Box sx={{ mt: 3 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Uploaded Files ({uploadedFiles.length})
                  </Typography>
                  {uploadedFiles.map((file, index) => (
                    <Chip
                      key={index}
                      label={file.name}
                      variant="outlined"
                      sx={{ mr: 1, mb: 1 }}
                      onDelete={() => {
                        setUploadedFiles(files => files.filter((_, i) => i !== index))
                      }}
                    />
                  ))}
                </Box>
              )}
            </Box>
          </motion.div>
        )

      case 3:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Box>
              <Typography variant="h6" gutterBottom>
                Review Your Application
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Please review all information before submitting your application.
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                        Basic Information
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Type:</Typography>
                          <Typography variant="body2">{watchBasic().type}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Priority:</Typography>
                          <Chip label={watchBasic().priority} size="small" />
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Company:</Typography>
                          <Typography variant="body2">{watchBasic().companyName}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Contact:</Typography>
                          <Typography variant="body2">{watchBasic().contactPerson}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Email:</Typography>
                          <Typography variant="body2">{watchBasic().email}</Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                        Cargo Details
                      </Typography>
                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                        <Box>
                          <Typography variant="body2" color="text.secondary">Description:</Typography>
                          <Typography variant="body2">{watchCargo().cargoDescription}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">HS Code:</Typography>
                          <Typography variant="body2">{watchCargo().hsCode}</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Quantity:</Typography>
                          <Typography variant="body2">
                            {watchCargo().quantity} {watchCargo().unit}
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Weight:</Typography>
                          <Typography variant="body2">{watchCargo().weight} kg</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" color="text.secondary">Value:</Typography>
                          <Typography variant="body2">
                            {watchCargo().currency} {watchCargo().value?.toLocaleString()}
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
                        Documents ({uploadedFiles.length})
                      </Typography>
                      {uploadedFiles.length > 0 ? (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {uploadedFiles.map((file, index) => (
                            <Chip
                              key={index}
                              label={file.name}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          No documents uploaded
                        </Typography>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          </motion.div>
        )

      default:
        return null
    }
  }

  return (
    <Box sx={{ maxWidth: 900, mx: 'auto', p: 3 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, mb: 4 }}>
          Create New Application
        </Typography>

        {loading && (
          <Box sx={{ mb: 2 }}>
            <LinearProgress />
          </Box>
        )}

        <Stepper activeStep={activeStep} orientation="vertical">
          {steps.map((step, index) => (
            <Step key={step.label}>
              <StepLabel
                optional={
                  index === steps.length - 1 ? (
                    <Typography variant="caption">Last step</Typography>
                  ) : null
                }
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {step.icon}
                  <Box>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                      {step.label}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {step.description}
                    </Typography>
                  </Box>
                </Box>
              </StepLabel>
              <StepContent>
                <AnimatePresence mode="wait">
                  <Box sx={{ mb: 3 }}>
                    {renderStepContent(index)}
                  </Box>
                </AnimatePresence>
                
                <Box sx={{ display: 'flex', gap: 2, mt: 3 }}>
                  <Button
                    disabled={index === 0}
                    onClick={handleBack}
                    variant="outlined"
                  >
                    Back
                  </Button>
                  
                  {index === steps.length - 1 ? (
                    <Button
                      variant="contained"
                      onClick={handleFinalSubmit}
                      disabled={loading}
                      startIcon={<SubmitIcon />}
                      size="large"
                    >
                      {loading ? 'Submitting...' : 'Submit Application'}
                    </Button>
                  ) : (
                    <Button
                      variant="contained"
                      onClick={() => {
                        if (index === 0) {
                          handleBasicSubmit(handleBasicInfoNext)()
                        } else if (index === 1) {
                          handleCargoSubmit(handleCargoDetailsNext)()
                        } else {
                          handleNext()
                        }
                      }}
                      size="large"
                    >
                      Continue
                    </Button>
                  )}
                  
                  <Button
                    variant="text"
                    startIcon={<SaveIcon />}
                    onClick={() => {
                      toast.info('Draft saved locally')
                    }}
                  >
                    Save Draft
                  </Button>
                </Box>
              </StepContent>
            </Step>
          ))}
        </Stepper>
      </motion.div>
    </Box>
  )
}

export default NewApplicationPage
