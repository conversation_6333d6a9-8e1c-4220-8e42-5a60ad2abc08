import React from 'react'
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Avatar,
  Chip,
  LinearProgress,
  useTheme,
} from '@mui/material'
import {
  TrendingUp as TrendingUpIcon,
  Description as ApplicationsIcon,
  Payment as PaymentIcon,
  LocalShipping as ShippingIcon,
  CheckCircle as CompletedIcon,
  Schedule as PendingIcon,
  Warning as WarningIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'

import { useAuth } from '@/hooks/useAuth'

// Mock data - replace with real API calls
const mockStats = {
  totalApplications: 1247,
  pendingApplications: 89,
  completedApplications: 1158,
  totalRevenue: 2450000,
  averageProcessingTime: 3.2,
  recentApplications: [
    {
      id: 'APP-2024-001',
      type: 'Import',
      status: 'Under Review',
      submissionDate: '2024-01-15',
      applicant: 'ABC Trading Ltd',
    },
    {
      id: 'APP-2024-002',
      type: 'Export',
      status: 'Completed',
      submissionDate: '2024-01-14',
      applicant: 'XYZ Exports Inc',
    },
  ],
}

const DashboardPage: React.FC = () => {
  const theme = useTheme()
  const { user } = useAuth()

  const statsCards = [
    {
      title: 'Total Applications',
      value: mockStats.totalApplications.toLocaleString(),
      icon: <ApplicationsIcon />,
      color: theme.palette.primary.main,
      trend: '+12%',
    },
    {
      title: 'Pending Review',
      value: mockStats.pendingApplications.toLocaleString(),
      icon: <PendingIcon />,
      color: theme.palette.warning.main,
      trend: '-5%',
    },
    {
      title: 'Completed',
      value: mockStats.completedApplications.toLocaleString(),
      icon: <CompletedIcon />,
      color: theme.palette.success.main,
      trend: '+8%',
    },
    {
      title: 'Total Revenue',
      value: `₦${(mockStats.totalRevenue / 1000000).toFixed(1)}M`,
      icon: <PaymentIcon />,
      color: theme.palette.info.main,
      trend: '+15%',
    },
  ]

  return (
    <Box sx={{ p: 3 }}>
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
            Welcome back, {user?.firstName}!
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Here's what's happening with your EXIM operations today.
          </Typography>
        </Box>
      </motion.div>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {statsCards.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={stat.title}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card
                sx={{
                  height: '100%',
                  background: `linear-gradient(135deg, ${stat.color}15 0%, ${stat.color}05 100%)`,
                  border: `1px solid ${stat.color}20`,
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: theme.shadows[8],
                  },
                  transition: 'all 0.3s ease-in-out',
                }}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      sx={{
                        bgcolor: stat.color,
                        width: 48,
                        height: 48,
                        mr: 2,
                      }}
                    >
                      {stat.icon}
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="h4" sx={{ fontWeight: 700 }}>
                        {stat.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {stat.title}
                      </Typography>
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Chip
                      label={stat.trend}
                      size="small"
                      color={stat.trend.startsWith('+') ? 'success' : 'error'}
                      variant="outlined"
                    />
                    <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                      vs last month
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Main Content Grid */}
      <Grid container spacing={3}>
        {/* Recent Applications */}
        <Grid item xs={12} md={8}>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                Recent Applications
              </Typography>
              <Box sx={{ space: 2 }}>
                {mockStats.recentApplications.map((app, index) => (
                  <Box
                    key={app.id}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      p: 2,
                      borderRadius: 2,
                      border: `1px solid ${theme.palette.divider}`,
                      mb: 2,
                      '&:hover': {
                        backgroundColor: theme.palette.action.hover,
                      },
                    }}
                  >
                    <Avatar
                      sx={{
                        bgcolor: app.status === 'Completed' 
                          ? theme.palette.success.main 
                          : theme.palette.warning.main,
                        mr: 2,
                      }}
                    >
                      {app.status === 'Completed' ? <CompletedIcon /> : <PendingIcon />}
                    </Avatar>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                        {app.id}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {app.applicant} • {app.type}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Submitted: {app.submissionDate}
                      </Typography>
                    </Box>
                    <Chip
                      label={app.status}
                      size="small"
                      color={app.status === 'Completed' ? 'success' : 'warning'}
                      variant="outlined"
                    />
                  </Box>
                ))}
              </Box>
            </Paper>
          </motion.div>
        </Grid>

        {/* Quick Actions & Processing Time */}
        <Grid item xs={12} md={4}>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                Average Processing Time
              </Typography>
              <Box sx={{ textAlign: 'center', mb: 2 }}>
                <Typography variant="h3" sx={{ fontWeight: 700, color: theme.palette.primary.main }}>
                  {mockStats.averageProcessingTime}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  days
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={75}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: theme.palette.grey[200],
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 4,
                  },
                }}
              />
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                25% faster than target (4.2 days)
              </Typography>
            </Paper>

            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                System Status
              </Typography>
              <Box sx={{ space: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box
                    sx={{
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      bgcolor: theme.palette.success.main,
                      mr: 2,
                    }}
                  />
                  <Typography variant="body2">All systems operational</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Box
                    sx={{
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      bgcolor: theme.palette.success.main,
                      mr: 2,
                    }}
                  />
                  <Typography variant="body2">Payment gateway online</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box
                    sx={{
                      width: 12,
                      height: 12,
                      borderRadius: '50%',
                      bgcolor: theme.palette.warning.main,
                      mr: 2,
                    }}
                  />
                  <Typography variant="body2">Customs API: Slow response</Typography>
                </Box>
              </Box>
            </Paper>
          </motion.div>
        </Grid>
      </Grid>
    </Box>
  )
}

export default DashboardPage
