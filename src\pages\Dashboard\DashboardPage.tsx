import React from 'react'
import {
  Box,
  Grid,
  Paper,
  Typography,
  Avatar,
  Chip,
  useTheme,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
} from '@mui/material'
import {
  Description as ApplicationsIcon,
  Payment as PaymentIcon,
  LocalShipping as ShippingIcon,
  CheckCircle as CompletedIcon,
  Schedule as PendingIcon,
  Warning as WarningIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as ReportsIcon,
} from '@mui/icons-material'
import { motion } from 'framer-motion'

import { useAuth } from '@/hooks/useAuth'
import StatsCard from '@/components/Dashboard/StatsCard'
import ChartCard from '@/components/Dashboard/ChartCard'
import StatusChip from '@/components/Common/StatusChip'

// Mock data - replace with real API calls
const mockStats = {
  totalApplications: 1247,
  pendingApplications: 89,
  completedApplications: 1158,
  totalRevenue: 2450000,
  averageProcessingTime: 3.2,
  recentApplications: [
    {
      id: 'APP-2024-001',
      type: 'Import',
      status: 'under_review',
      submissionDate: '2024-01-15',
      applicant: 'ABC Trading Ltd',
      priority: 'high',
    },
    {
      id: 'APP-2024-002',
      type: 'Export',
      status: 'completed',
      submissionDate: '2024-01-14',
      applicant: 'XYZ Exports Inc',
      priority: 'normal',
    },
    {
      id: 'APP-2024-003',
      type: 'Import',
      status: 'pending_payment',
      submissionDate: '2024-01-13',
      applicant: 'Global Imports Co',
      priority: 'urgent',
    },
  ],
  monthlyData: [
    { month: 'Jan', applications: 120, revenue: 180000 },
    { month: 'Feb', applications: 98, revenue: 150000 },
    { month: 'Mar', applications: 156, revenue: 220000 },
    { month: 'Apr', applications: 134, revenue: 195000 },
    { month: 'May', applications: 178, revenue: 265000 },
    { month: 'Jun', applications: 145, revenue: 210000 },
  ],
  statusDistribution: [
    { name: 'Completed', value: 65, count: 812 },
    { name: 'Pending', value: 20, count: 249 },
    { name: 'In Review', value: 10, count: 125 },
    { name: 'Rejected', value: 5, count: 61 },
  ],
}

const DashboardPage: React.FC = () => {
  const theme = useTheme()
  const { user } = useAuth()

  const statsCards = [
    {
      title: 'Total Applications',
      value: mockStats.totalApplications.toLocaleString(),
      icon: <ApplicationsIcon />,
      color: theme.palette.primary.main,
      trend: { value: 12, label: 'vs last month', isPositive: true },
      progress: { value: 75, label: 'Monthly target' },
    },
    {
      title: 'Pending Review',
      value: mockStats.pendingApplications.toLocaleString(),
      icon: <PendingIcon />,
      color: theme.palette.warning.main,
      trend: { value: -5, label: 'vs last month', isPositive: false },
      subtitle: 'Requires attention',
    },
    {
      title: 'Completed',
      value: mockStats.completedApplications.toLocaleString(),
      icon: <CompletedIcon />,
      color: theme.palette.success.main,
      trend: { value: 8, label: 'vs last month', isPositive: true },
      progress: { value: 92, label: 'Success rate' },
    },
    {
      title: 'Total Revenue',
      value: `₦${(mockStats.totalRevenue / 1000000).toFixed(1)}M`,
      icon: <PaymentIcon />,
      color: theme.palette.info.main,
      trend: { value: 15, label: 'vs last month', isPositive: true },
      subtitle: 'This month',
    },
  ]

  return (
    <Box sx={{ p: 3 }}>
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
            Welcome back, {user?.firstName}!
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Here's what's happening with your EXIM operations today.
          </Typography>
        </Box>
      </motion.div>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {statsCards.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={stat.title}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <StatsCard {...stat} />
            </motion.div>
          </Grid>
        ))}
      </Grid>

      {/* Main Content Grid */}
      <Grid container spacing={3}>
        {/* Charts Row */}
        <Grid item xs={12} md={8}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <ChartCard
                title="Applications Trend"
                subtitle="Monthly application submissions and revenue"
                type="area"
                data={mockStats.monthlyData}
                dataKey="applications"
                xAxisKey="month"
                height={300}
              />
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs={12} md={4}>
          <ChartCard
            title="Status Distribution"
            subtitle="Current application status breakdown"
            type="pie"
            data={mockStats.statusDistribution}
            dataKey="value"
            height={300}
          />
        </Grid>

        {/* Recent Applications */}
        <Grid item xs={12} md={8}>
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Paper sx={{ p: 3, height: '100%' }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                Recent Applications
              </Typography>
              <List>
                {mockStats.recentApplications.map((app, index) => (
                  <React.Fragment key={app.id}>
                    <ListItem
                      sx={{
                        borderRadius: 2,
                        '&:hover': {
                          backgroundColor: theme.palette.action.hover,
                        },
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar
                          sx={{
                            bgcolor: app.status === 'completed'
                              ? theme.palette.success.main
                              : app.status === 'pending_payment'
                              ? theme.palette.warning.main
                              : theme.palette.info.main,
                          }}
                        >
                          {app.status === 'completed' ? <CompletedIcon /> :
                           app.status === 'pending_payment' ? <PaymentIcon /> : <PendingIcon />}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                              {app.id}
                            </Typography>
                            <StatusChip status={app.priority} />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {app.applicant} • {app.type}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Submitted: {app.submissionDate}
                            </Typography>
                          </Box>
                        }
                      />
                      <StatusChip status={app.status} />
                    </ListItem>
                    {index < mockStats.recentApplications.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Paper>
          </motion.div>
        </Grid>

        {/* Performance Metrics & System Status */}
        <Grid item xs={12} md={4}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                    Performance Metrics
                  </Typography>

                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Processing Time</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {mockStats.averageProcessingTime} days
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Success Rate</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        92.8%
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2">Customer Satisfaction</Typography>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        4.6/5.0
                      </Typography>
                    </Box>
                  </Box>

                  <Divider sx={{ mb: 3 }} />

                  <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
                    System Status
                  </Typography>
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          bgcolor: theme.palette.success.main,
                          mr: 2,
                        }}
                      />
                      <Typography variant="body2">All systems operational</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          bgcolor: theme.palette.success.main,
                          mr: 2,
                        }}
                      />
                      <Typography variant="body2">Payment gateway online</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          bgcolor: theme.palette.warning.main,
                          mr: 2,
                        }}
                      />
                      <Typography variant="body2">Customs API: Slow response</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          bgcolor: theme.palette.info.main,
                          mr: 2,
                        }}
                      />
                      <Typography variant="body2">Blockchain sync: 99.2%</Typography>
                    </Box>
                  </Box>
                </Paper>
              </motion.div>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  )
}

export default DashboardPage
