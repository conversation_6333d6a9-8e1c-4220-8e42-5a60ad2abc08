import { apiService } from './api'
import { User, UserRole } from '@/types'

// Auth Service Types
interface LoginRequest {
  email: string
  password: string
  mfaCode?: string
}

interface LoginResponse {
  user: User
  token: string
  refreshToken: string
  expiresIn: number
}

interface RegisterRequest {
  email: string
  password: string
  firstName: string
  lastName: string
  role: UserRole
  organization?: string
  phone?: string
}

interface RefreshTokenResponse {
  user: User
  token: string
  expiresIn: number
}

interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

interface ResetPasswordRequest {
  email: string
}

interface ConfirmResetPasswordRequest {
  token: string
  newPassword: string
}

interface MfaSetupResponse {
  qrCode: string
  secret: string
  backupCodes: string[]
}

interface VerifyMfaRequest {
  code: string
  secret: string
}

// Auth Service Class
class AuthService {
  // Login user
  async login(email: string, password: string, mfaCode?: string): Promise<LoginResponse> {
    const request: LoginRequest = { email, password, mfaCode }
    const response = await apiService.post<LoginResponse>('/auth/login', request)
    
    if (response.success && response.data) {
      // Store refresh token
      localStorage.setItem('refresh_token', response.data.refreshToken)
      return response.data
    }
    
    throw new Error(response.message || 'Login failed')
  }

  // Register new user
  async register(userData: RegisterRequest): Promise<void> {
    const response = await apiService.post('/auth/register', userData)
    
    if (!response.success) {
      throw new Error(response.message || 'Registration failed')
    }
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      await apiService.post('/auth/logout')
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error)
    } finally {
      localStorage.removeItem('auth_token')
      localStorage.removeItem('refresh_token')
    }
  }

  // Get current user
  async getCurrentUser(): Promise<User> {
    const response = await apiService.get<User>('/auth/me')
    
    if (response.success && response.data) {
      return response.data
    }
    
    throw new Error(response.message || 'Failed to get user data')
  }

  // Refresh authentication token
  async refreshToken(): Promise<RefreshTokenResponse> {
    const refreshToken = localStorage.getItem('refresh_token')
    
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await apiService.post<RefreshTokenResponse>('/auth/refresh', {
      refreshToken,
    })
    
    if (response.success && response.data) {
      return response.data
    }
    
    throw new Error(response.message || 'Token refresh failed')
  }

  // Update user profile
  async updateProfile(userData: Partial<User>): Promise<User> {
    const response = await apiService.patch<User>('/auth/profile', userData)
    
    if (response.success && response.data) {
      return response.data
    }
    
    throw new Error(response.message || 'Profile update failed')
  }

  // Change password
  async changePassword(passwordData: ChangePasswordRequest): Promise<void> {
    const response = await apiService.post('/auth/change-password', passwordData)
    
    if (!response.success) {
      throw new Error(response.message || 'Password change failed')
    }
  }

  // Request password reset
  async requestPasswordReset(email: string): Promise<void> {
    const request: ResetPasswordRequest = { email }
    const response = await apiService.post('/auth/reset-password', request)
    
    if (!response.success) {
      throw new Error(response.message || 'Password reset request failed')
    }
  }

  // Confirm password reset
  async confirmPasswordReset(token: string, newPassword: string): Promise<void> {
    const request: ConfirmResetPasswordRequest = { token, newPassword }
    const response = await apiService.post('/auth/reset-password/confirm', request)
    
    if (!response.success) {
      throw new Error(response.message || 'Password reset confirmation failed')
    }
  }

  // Setup MFA
  async setupMfa(): Promise<MfaSetupResponse> {
    const response = await apiService.post<MfaSetupResponse>('/auth/mfa/setup')
    
    if (response.success && response.data) {
      return response.data
    }
    
    throw new Error(response.message || 'MFA setup failed')
  }

  // Verify MFA setup
  async verifyMfaSetup(code: string, secret: string): Promise<void> {
    const request: VerifyMfaRequest = { code, secret }
    const response = await apiService.post('/auth/mfa/verify', request)
    
    if (!response.success) {
      throw new Error(response.message || 'MFA verification failed')
    }
  }

  // Disable MFA
  async disableMfa(password: string): Promise<void> {
    const response = await apiService.post('/auth/mfa/disable', { password })
    
    if (!response.success) {
      throw new Error(response.message || 'MFA disable failed')
    }
  }

  // Verify email
  async verifyEmail(token: string): Promise<void> {
    const response = await apiService.post('/auth/verify-email', { token })
    
    if (!response.success) {
      throw new Error(response.message || 'Email verification failed')
    }
  }

  // Resend email verification
  async resendEmailVerification(): Promise<void> {
    const response = await apiService.post('/auth/resend-verification')
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to resend verification email')
    }
  }

  // Check if email exists
  async checkEmailExists(email: string): Promise<boolean> {
    try {
      const response = await apiService.get(`/auth/check-email?email=${encodeURIComponent(email)}`)
      return response.data?.exists || false
    } catch {
      return false
    }
  }

  // Validate token
  async validateToken(token: string): Promise<boolean> {
    try {
      const response = await apiService.post('/auth/validate-token', { token })
      return response.success
    } catch {
      return false
    }
  }

  // Get user permissions
  async getUserPermissions(): Promise<string[]> {
    const response = await apiService.get<string[]>('/auth/permissions')
    
    if (response.success && response.data) {
      return response.data
    }
    
    return []
  }

  // Check if user has permission
  async hasPermission(resource: string, action: string): Promise<boolean> {
    try {
      const response = await apiService.get(
        `/auth/check-permission?resource=${resource}&action=${action}`
      )
      return response.data?.hasPermission || false
    } catch {
      return false
    }
  }
}

// Export singleton instance
export const authService = new AuthService()
export default authService
