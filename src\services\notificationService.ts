import { apiService } from './api'
import { Notification, NotificationType, Priority } from '@/types'

// Notification Service Types
interface CreateNotificationRequest {
  userId: string
  title: string
  message: string
  type: NotificationType
  priority: Priority
  actionUrl?: string
  metadata?: Record<string, any>
  expiresAt?: Date
}

interface NotificationPreferences {
  email: boolean
  sms: boolean
  push: boolean
  inApp: boolean
  types: NotificationType[]
}

// Notification Service Class
class NotificationService {
  // Get notifications for user
  async getNotifications(userId: string, page = 1, limit = 20): Promise<Notification[]> {
    const response = await apiService.get<Notification[]>(
      `/notifications?userId=${userId}&page=${page}&limit=${limit}`
    )
    
    if (response.success && response.data) {
      return response.data
    }
    
    return []
  }

  // Get unread notifications count
  async getUnreadCount(userId: string): Promise<number> {
    const response = await apiService.get<{ count: number }>(
      `/notifications/unread-count?userId=${userId}`
    )
    
    if (response.success && response.data) {
      return response.data.count
    }
    
    return 0
  }

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<void> {
    const response = await apiService.patch(`/notifications/${notificationId}/read`)
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to mark notification as read')
    }
  }

  // Mark all notifications as read for user
  async markAllAsRead(userId: string): Promise<void> {
    const response = await apiService.patch(`/notifications/mark-all-read`, { userId })
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to mark all notifications as read')
    }
  }

  // Delete notification
  async deleteNotification(notificationId: string): Promise<void> {
    const response = await apiService.delete(`/notifications/${notificationId}`)
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to delete notification')
    }
  }

  // Create notification
  async createNotification(notificationData: CreateNotificationRequest): Promise<Notification> {
    const response = await apiService.post<Notification>('/notifications', notificationData)
    
    if (response.success && response.data) {
      return response.data
    }
    
    throw new Error(response.message || 'Failed to create notification')
  }

  // Send bulk notifications
  async sendBulkNotifications(
    userIds: string[],
    notificationData: Omit<CreateNotificationRequest, 'userId'>
  ): Promise<void> {
    const response = await apiService.post('/notifications/bulk', {
      userIds,
      ...notificationData,
    })
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to send bulk notifications')
    }
  }

  // Get notification preferences
  async getPreferences(userId: string): Promise<NotificationPreferences> {
    const response = await apiService.get<NotificationPreferences>(
      `/notifications/preferences?userId=${userId}`
    )
    
    if (response.success && response.data) {
      return response.data
    }
    
    // Return default preferences
    return {
      email: true,
      sms: false,
      push: true,
      inApp: true,
      types: Object.values(NotificationType),
    }
  }

  // Update notification preferences
  async updatePreferences(userId: string, preferences: NotificationPreferences): Promise<void> {
    const response = await apiService.put('/notifications/preferences', {
      userId,
      ...preferences,
    })
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to update notification preferences')
    }
  }

  // Subscribe to real-time notifications (Server-Sent Events)
  subscribeToNotifications(userId: string): EventSource {
    const token = localStorage.getItem('auth_token')
    const url = `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:4000/api'}/notifications/stream?userId=${userId}&token=${token}`
    
    const eventSource = new EventSource(url)
    
    eventSource.onerror = (error) => {
      console.error('Notification stream error:', error)
    }
    
    return eventSource
  }

  // Send test notification
  async sendTestNotification(userId: string): Promise<void> {
    const testNotification: CreateNotificationRequest = {
      userId,
      title: 'Test Notification',
      message: 'This is a test notification to verify your notification settings.',
      type: NotificationType.INFO,
      priority: Priority.LOW,
    }
    
    await this.createNotification(testNotification)
  }

  // Get notification templates
  async getTemplates(): Promise<any[]> {
    const response = await apiService.get('/notifications/templates')
    
    if (response.success && response.data) {
      return response.data
    }
    
    return []
  }

  // Create notification template
  async createTemplate(template: any): Promise<any> {
    const response = await apiService.post('/notifications/templates', template)
    
    if (response.success && response.data) {
      return response.data
    }
    
    throw new Error(response.message || 'Failed to create notification template')
  }

  // Send notification from template
  async sendFromTemplate(
    templateId: string,
    userId: string,
    variables: Record<string, any>
  ): Promise<void> {
    const response = await apiService.post('/notifications/send-template', {
      templateId,
      userId,
      variables,
    })
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to send notification from template')
    }
  }

  // Get notification statistics
  async getStatistics(userId: string, dateRange?: { start: Date; end: Date }): Promise<any> {
    let url = `/notifications/statistics?userId=${userId}`
    
    if (dateRange) {
      url += `&start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`
    }
    
    const response = await apiService.get(url)
    
    if (response.success && response.data) {
      return response.data
    }
    
    return {}
  }

  // Archive old notifications
  async archiveOldNotifications(userId: string, olderThanDays = 30): Promise<void> {
    const response = await apiService.post('/notifications/archive', {
      userId,
      olderThanDays,
    })
    
    if (!response.success) {
      throw new Error(response.message || 'Failed to archive old notifications')
    }
  }
}

// Export singleton instance
export const notificationService = new NotificationService()
export default notificationService
