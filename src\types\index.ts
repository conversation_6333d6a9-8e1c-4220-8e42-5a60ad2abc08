// Core types for EXIM Clearance Certification System

// User and Authentication Types
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  organization?: string
  phone?: string
  avatar?: string
  isActive: boolean
  lastLogin?: Date
  createdAt: Date
  updatedAt: Date
  permissions: Permission[]
}

export enum UserRole {
  APPLICANT = 'applicant',
  CUSTOMS_OFFICER = 'customs_officer',
  SHIPPING_AGENT = 'shipping_agent',
  PORT_OPERATOR = 'port_operator',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
}

export interface Permission {
  id: string
  name: string
  resource: string
  action: string
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

// Application Types
export interface Application {
  id: string
  applicationNumber: string
  applicantId: string
  applicant: User
  type: ApplicationType
  status: ApplicationStatus
  priority: Priority
  submissionDate: Date
  expectedCompletionDate?: Date
  completionDate?: Date
  documents: Document[]
  payments: Payment[]
  trackingEvents: TrackingEvent[]
  customsDeclaration?: CustomsDeclaration
  cargo: CargoDetails
  createdAt: Date
  updatedAt: Date
}

export enum ApplicationType {
  IMPORT = 'import',
  EXPORT = 'export',
  TRANSIT = 'transit',
  TEMPORARY_IMPORT = 'temporary_import',
  RE_EXPORT = 're_export',
}

export enum ApplicationStatus {
  DRAFT = 'draft',
  SUBMITTED = 'submitted',
  UNDER_REVIEW = 'under_review',
  PENDING_PAYMENT = 'pending_payment',
  PAYMENT_CONFIRMED = 'payment_confirmed',
  CUSTOMS_CLEARANCE = 'customs_clearance',
  INSPECTION_REQUIRED = 'inspection_required',
  INSPECTION_COMPLETED = 'inspection_completed',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum Priority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

// Document Types
export interface Document {
  id: string
  applicationId: string
  name: string
  type: DocumentType
  fileName: string
  fileSize: number
  mimeType: string
  url: string
  uploadedBy: string
  uploadedAt: Date
  status: DocumentStatus
  validationResults?: ValidationResult[]
  qrCode?: string
  digitalSignature?: DigitalSignature
  version: number
  isRequired: boolean
}

export enum DocumentType {
  COMMERCIAL_INVOICE = 'commercial_invoice',
  BILL_OF_LADING = 'bill_of_lading',
  PACKING_LIST = 'packing_list',
  CERTIFICATE_OF_ORIGIN = 'certificate_of_origin',
  INSURANCE_CERTIFICATE = 'insurance_certificate',
  FORM_M = 'form_m',
  PAAR = 'paar',
  SONCAP_CERTIFICATE = 'soncap_certificate',
  NAFDAC_CERTIFICATE = 'nafdac_certificate',
  CUSTOMS_DECLARATION = 'customs_declaration',
  PAYMENT_RECEIPT = 'payment_receipt',
  OTHER = 'other',
}

export enum DocumentStatus {
  UPLOADED = 'uploaded',
  VALIDATING = 'validating',
  VALID = 'valid',
  INVALID = 'invalid',
  EXPIRED = 'expired',
  REJECTED = 'rejected',
}

export interface ValidationResult {
  field: string
  status: 'valid' | 'invalid' | 'warning'
  message: string
  confidence?: number
}

export interface DigitalSignature {
  signedBy: string
  signedAt: Date
  signature: string
  certificate: string
}

// Payment Types
export interface Payment {
  id: string
  applicationId: string
  amount: number
  currency: string
  type: PaymentType
  status: PaymentStatus
  method: PaymentMethod
  reference: string
  description: string
  dueDate?: Date
  paidAt?: Date
  gateway: string
  gatewayReference?: string
  receipt?: string
  createdAt: Date
  updatedAt: Date
}

export enum PaymentType {
  CUSTOMS_DUTY = 'customs_duty',
  VAT = 'vat',
  PROCESSING_FEE = 'processing_fee',
  STORAGE_FEE = 'storage_fee',
  INSPECTION_FEE = 'inspection_fee',
  PENALTY = 'penalty',
  OTHER = 'other',
}

export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded',
  CANCELLED = 'cancelled',
}

export enum PaymentMethod {
  BANK_TRANSFER = 'bank_transfer',
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  MOBILE_MONEY = 'mobile_money',
  CASH = 'cash',
}

// Cargo and Tracking Types
export interface CargoDetails {
  id: string
  description: string
  hsCode: string
  quantity: number
  unit: string
  weight: number
  value: number
  currency: string
  origin: string
  destination: string
  containerNumber?: string
  sealNumber?: string
  vessel?: VesselInfo
  estimatedArrival?: Date
  actualArrival?: Date
}

export interface VesselInfo {
  name: string
  imoNumber: string
  flag: string
  operator: string
  voyage: string
}

export interface TrackingEvent {
  id: string
  applicationId: string
  timestamp: Date
  location: Location
  status: string
  description: string
  performedBy?: string
  metadata?: Record<string, any>
}

export interface Location {
  name: string
  code: string
  coordinates?: {
    latitude: number
    longitude: number
  }
  address?: string
}

// Customs Types
export interface CustomsDeclaration {
  id: string
  applicationId: string
  declarationNumber: string
  declarationType: string
  totalValue: number
  currency: string
  dutyAmount: number
  vatAmount: number
  otherCharges: number
  items: CustomsItem[]
  createdAt: Date
  updatedAt: Date
}

export interface CustomsItem {
  id: string
  description: string
  hsCode: string
  quantity: number
  unit: string
  unitValue: number
  totalValue: number
  dutyRate: number
  dutyAmount: number
  vatRate: number
  vatAmount: number
}

// Notification Types
export interface Notification {
  id: string
  userId: string
  title: string
  message: string
  type: NotificationType
  priority: Priority
  isRead: boolean
  actionUrl?: string
  metadata?: Record<string, any>
  createdAt: Date
  expiresAt?: Date
}

export enum NotificationType {
  INFO = 'info',
  SUCCESS = 'success',
  WARNING = 'warning',
  ERROR = 'error',
  SYSTEM = 'system',
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  errors?: string[]
  pagination?: PaginationInfo
}

export interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

// Form Types
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'multiselect' | 'date' | 'file' | 'textarea'
  required: boolean
  validation?: any
  options?: { label: string; value: any }[]
  placeholder?: string
  helperText?: string
}

// Dashboard Types
export interface DashboardStats {
  totalApplications: number
  pendingApplications: number
  completedApplications: number
  totalRevenue: number
  averageProcessingTime: number
  recentApplications: Application[]
  statusDistribution: { status: ApplicationStatus; count: number }[]
  monthlyTrends: { month: string; applications: number; revenue: number }[]
}

// Filter and Search Types
export interface FilterOptions {
  status?: ApplicationStatus[]
  type?: ApplicationType[]
  dateRange?: {
    start: Date
    end: Date
  }
  priority?: Priority[]
  search?: string
}

export interface SortOptions {
  field: string
  direction: 'asc' | 'desc'
}
