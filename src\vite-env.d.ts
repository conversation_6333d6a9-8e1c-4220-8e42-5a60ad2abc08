/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
  readonly VITE_API_TIMEOUT: string
  readonly VITE_ENABLE_AI_FEATURES: string
  readonly VITE_ENABLE_BLOCKCHAIN: string
  readonly VITE_ENABLE_REAL_TIME_TRACKING: string
  readonly VITE_ENABLE_ADVANCED_ANALYTICS: string
  readonly VITE_DEBUG_MODE: string
  readonly VITE_MOCK_API: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
